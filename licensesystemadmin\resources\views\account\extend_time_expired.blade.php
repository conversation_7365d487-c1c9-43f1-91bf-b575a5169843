@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.extend_time_expired.account.game'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('account') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.account.game') }}
        </a>
    </li>

@endsection

@section('content')

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('account.update_time_expired', ['id' => $account->id]) }}" method="post">
                        {{ csrf_field() }}
                        <input type="hidden" name="active" value="1">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.time_expired') }}</label>
                                    <input type="date" name="time_expired" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.time_expired') }}" required="" value="{{ $timeExpired != null ? $timeExpired->format('Y-m-d') : now()->format('Y-m-d') }}" autofocus>
                                    @if($errors->has('time_expired'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('time_expired') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.extend') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

<script>
    $(function(){
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return "{{ __('404.not_found') }}";
                }
            }
        });
    });

</script>

@endsection
