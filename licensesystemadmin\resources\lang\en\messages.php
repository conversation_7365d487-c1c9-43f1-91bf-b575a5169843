<?php

return [
    'allow_user_register_via_website' => 'Allows new users via the register screen',
    'default_role_register' => 'Default permission for new users when registering',

    // General actions
    'edit' => 'Edit',
    'delete' => 'Delete',
    'cancel' => 'Cancel',
    'action' => [
        'save' => 'Save',
        'cancel' => 'Cancel',
        'edit' => 'Edit'
    ],

    // School information
    'school_info' => 'School Information',
    'edit_school' => 'Edit School',
    'name' => 'Name',
    'province' => 'Province',
    'district' => 'District',
    'software_type' => 'Software Type',
    'time_expired' => 'Expiration Date',
    'select' => 'Select',
    'software_mamnon' => 'Kindergarten Software',
    'software_tieuhoc' => 'Primary School Software',
    'mamnon_account_type' => 'Kindergarten Account Type',
    'tieuhoc_account_type' => 'Primary School Account Type',

    // Account information
    'accounts' => 'Accounts',
    'email' => 'Email',
    'phone_number' => 'Phone Number',
    'status' => 'Status',
    'status.active' => 'Active',
    'status.locked' => 'Locked',

    // Bulk actions
    'bulk_lock_unlock' => 'Bulk Lock/Unlock',
    'all_accounts_locked' => 'All Accounts Locked - Click to Unlock All',
    'all_accounts_unlocked' => 'All Accounts Unlocked - Click to Lock All',
    'mixed_account_status' => 'Mixed Account Status - Click to Lock All',
    'no_accounts' => 'No Accounts',

    // Confirmation messages
    'warning' => 'Warning!',
    'confirm_save_changes' => 'Are you sure you want to save these changes?',
    'confirm_delete_account' => 'Are you sure you want to delete this account?',

    // Account detail page
    'account_info' => 'Account Information',
    'account_settings' => 'Account Settings',
    'edit_account' => 'Edit Account',
    'no_permission_edit_account' => 'You do not have permission to edit account information',
    'no_results' => 'No results found',
    'basic_info' => 'Basic Information',
    'location_info' => 'Location Information',
    'software_info' => 'Software Information',
    'time_info' => 'Time Information',

    // Account type view
    'lesson_count' => 'Lesson Count',
    'course_count' => 'Course Count',
    'view' => 'View',
    'action.close' => 'Close',
    'lesson_list' => 'Lesson List',
    'courses' => 'Courses',
    'description' => 'Description',
    'code' => 'Code',
    'added_date' => 'Added Date',
    'will_be_set_to_current_date' => 'Will be set to current date and time',
    'non_existent_materials' => 'Number of materials that do not exist in the database',
    'basic_information' => 'Basic Information',
    'add' => 'Add',
    'enter_lesson_name' => 'Enter lesson name or number',

    // Search and filter
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'has_materials' => 'Has Materials',
    'no_materials' => 'No Materials',
    'has_courses' => 'Has Courses',
    'no_courses' => 'No Courses',
    'per_page' => 'per page',
    'showing' => 'Showing',
    'to' => 'to',
    'of' => 'of',
    'entries' => 'entries',
    'for' => 'for',
    'material_count' => 'Material Count',
    'action' => 'Action',
    'view_all' => 'View All',
    'reset_filters' => 'Reset Filters',
    'no_results_found' => 'No results found',
    'first_page' => 'First Page',
    'last_page' => 'Last Page',
    'previous_page' => 'Previous Page',
    'next_page' => 'Next Page',
    'page' => 'Page',
    'pagination_info' => 'Showing :from to :to of :total entries',
    'filter_materials' => 'Filter Materials',
    'has_materials_filter' => 'Has Materials',
    'no_materials_filter' => 'No Materials',
    'has_courses_filter' => 'Has Courses',
    'no_courses_filter' => 'No Courses',
    'non_existent_materials' => 'Non-existent materials',
    'loading' => 'Loading...',
    'filter_by' => 'Filter by',
    'per_page' => 'per page',
    'edit_material' => 'Edit material',
    'create_material' => 'Create new material',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'school_type' => 'School Type',
    'dashboard' => 'Dashboard',
    'statistics' => 'Statistics',
    'school_type_dashboard' => 'School Type Dashboard',
    'province_dashboard' => 'Province Dashboard',
    'district_dashboard' => 'District Dashboard',
    'school_types' => 'School Types',
    'percentage' => 'Percentage',
    'count' => 'Count',
    'stt' => 'No.',
    'partner' => 'Partner',
    'customer' => 'Customer',
    'internal' => 'Internal',
    'device' => 'Devices',
    'total' => 'Total',
    'other' => 'Other'
];
