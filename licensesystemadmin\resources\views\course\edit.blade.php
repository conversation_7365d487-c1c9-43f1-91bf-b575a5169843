@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.edit.course'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('course') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.course') }}
        </a>
    </li>

@endsection

@section('content')

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('course.update', ['id' => $course->id]) }}" method="post" enctype="multipart/form-data">
                        {{ csrf_field() }}
                        <input type="hidden" name="_method" value="put">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('title_en') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.title') }}</label>
                                    <input type="text" name="title_en" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.title') }}" required="" value="{{ $course->title_en }}" autofocus>
                                    @if($errors->has('title_en'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('title_en') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('description_en') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.description') }}</label>
                                    <input type="text" name="description_en" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.description') }}" required="" value="{{ $course->description_en }}" autofocus>
                                    @if($errors->has('description_en'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('description_en') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('image') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.image') }}</label>
                                    <input type="file" name="image" class="form-control" placeholder="{{ __('messages.image') }}">
                                    <br />
                                    <div class="image">
                                        <img height="20%" width="20%" src="{{ asset('uploads/courses/' . $course->image) }}" alt="Course" />
                                    </div>
                                    @if($errors->has('image'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('image') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('thumbnail_image') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.thumbnail_image') }}</label>
                                    <input type="file" name="thumbnail_image" class="form-control" placeholder="{{ __('messages.thumbnail_image') }}">
                                    <div class="image">
                                        <img height="20%" width="20%" src="{{ asset('uploads/courses/thumbnails/' . $course->thumbnail_image) }}" alt="Course" />
                                    </div>
                                    @if($errors->has('thumbnail_image'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('thumbnail_image') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6"></div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.edit') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

<script>
    $(function(){
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return "Nenhum registro encontrado.";
                }
            }
        });
    });

</script>

@endsection
