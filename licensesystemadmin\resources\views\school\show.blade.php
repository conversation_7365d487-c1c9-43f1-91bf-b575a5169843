@extends('layouts.AdminLTE.index')

@section('icon_page', 'eye')

@section('title', __('messages.detail.school'))

@section('menu_pagina')
    <li role="presentation">
        <a href="{{ route('account', ['view_mode' => 'school']) }}" class="link_menu_page">
            <i class="fa fa-school"></i> {{ __('messages.index.school') }}
        </a>
    </li>
@endsection

@section('content')
    <style>
        .status-icon {
            margin-left: 5px;
            color: #3c8dbc;
        }
        .editable-field:disabled {
            background-color: #f9f9f9;
            cursor: default;
        }
        .editable-field:not(:disabled) {
            background-color: #fff;
            border-color: #3c8dbc;
        }
        .panel {
            border-radius: 3px;
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .panel-heading {
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
            padding: 10px 15px;
            background-color: #f5f5f5;
        }
        .panel-title {
            margin-top: 0;
            margin-bottom: 0;
            font-size: 16px;
            color: #333;
        }
        .panel-body {
            padding: 15px;
        }
        label {
            font-weight: 600;
            margin-bottom: 5px;
            display: block;
        }
        .form-group {
            margin-bottom: 15px;
        }
    </style>

    <div class="alert alert-info alert-dismissible">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
        <h4><i class="icon fa fa-info"></i> {{ __('messages.important_information') }}</h4>
        {{ __('messages.school_update_notice') }}
    </div>
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">{{ __('messages.school_info') ?: 'School Information' }}</h3>
            <div class="box-tools pull-right">
                @if(Auth::user()->can('edit-account', App\Models\School::class))
                <button type="button" id="edit-school-btn" class="btn btn-sm btn-primary" title="{{ __('messages.edit_school') }}">
                    <i class="fa fa-pencil"></i> {{ __('messages.edit') }}
                </button>
                @endif
            </div>
        </div>
        <div class="box-body">
            <form id="school-form" action="{{ route('school.update', ['id' => $school->id]) }}" method="post">
                {{ csrf_field() }}
                <input type="hidden" name="_method" value="put">
                @if(!Auth::user()->can('edit-account', App\Models\School::class))
                <input type="hidden" name="no_permission" value="true">
                @endif
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label>{{ __('messages.name') }} @if(Auth::user()->can('edit-account', App\Models\School::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                    <input type="text" name="name" class="form-control editable-field" value="{{ $school->name }}" disabled>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('messages.province') }} @if(Auth::user()->can('edit-account', App\Models\School::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                            <select name="province" id="province" class="form-control select2 editable-field" disabled>
                                                <option value="">{{ __('messages.select') }}</option>
                                                @foreach($provinces as $key => $province)
                                                    <option value="{{ $key }}" @if($school->province == $key) selected @endif>{{ $province['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{ __('messages.district') }} @if(Auth::user()->can('edit-account', App\Models\School::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                            <select name="district" id="district" class="form-control select2 editable-field" disabled>
                                                <option value="">{{ __('messages.select') }}</option>
                                                @foreach($districts as $key => $district)
                                                    <option value="{{ $key }}" @if($school->district == $key) selected @endif>{{ $district['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>{{ __('messages.software_type') }} @if(Auth::user()->can('edit-account', App\Models\School::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                    <select name="software_types[]" id="software-type" class="form-control select2 editable-field" multiple="multiple" data-placeholder="{{ __('messages.software_type') }}" disabled>
                                        <option value="{{ \App\Models\Account::SOFTWARE_MAMNON }}" @if($school->is_mamnon == 1) selected @endif>{{ __('messages.software_mamnon') }}</option>
                                        <option value="{{ \App\Models\Account::SOFTWARE_TIEUHOC }}" @if($school->is_tieuhoc == 1) selected @endif>{{ __('messages.software_tieuhoc') }}</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-lg-6">
                                <div id="mamnon-account-type" style="display: {{ $school->is_mamnon == 1 ? 'block' : 'none' }}">
                                    <div class="form-group">
                                        <label>{{ __('messages.mamnon_account_type') }} @if(Auth::user()->can('edit-account', App\Models\School::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                        <select name="mamnon_account_type" class="form-control select2 editable-field" data-placeholder="{{ __('messages.mamnon_account_type') }}" disabled>
                                            <option value="">{{ __('messages.select') }}</option>
                                            @foreach($mamnonAccountTypes as $accountType)
                                                <option value="{{ $accountType->id }}" @if($school->mamnon_account_type_id == $accountType->id) selected @endif>{{ $accountType->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div id="tieuhoc-account-type" style="display: {{ $school->is_tieuhoc == 1 ? 'block' : 'none' }}">
                                    <div class="form-group">
                                        <label>{{ __('messages.tieuhoc_account_type') }} @if(Auth::user()->can('edit-account', App\Models\School::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                        <select name="tieuhoc_account_type" class="form-control select2 editable-field" data-placeholder="{{ __('messages.tieuhoc_account_type') }}" disabled>
                                            <option value="">{{ __('messages.select') }}</option>
                                            @foreach($tieuhocAccountTypes as $accountType)
                                                <option value="{{ $accountType->id }}" @if($school->tieuhoc_account_type_id == $accountType->id) selected @endif>{{ $accountType->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>{{ __('messages.time_expired') }} @if(Auth::user()->can('edit-account', App\Models\School::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                    <input type="datetime-local" name="time_expired" class="form-control editable-field" value="{{ $school->time_expired ? date('Y-m-d\TH:i', strtotime($school->time_expired)) : '' }}" disabled>
                                    <small class="text-muted"><i class="fa fa-clock-o"></i> {{ __('messages.time_includes_hours_minutes') }}</small>
                                </div>
                            </div>
                        </div>

                        @if(Auth::user()->can('edit-account', App\Models\School::class))
                        <div class="row" style="margin-top: 20px;">
                            <div class="col-lg-12">
                                <div class="pull-left">
                                    <button type="button" id="cancel-changes-btn" class="btn btn-danger btn-flat" disabled>
                                        <i class="fa fa-times"></i> {{ __('messages.action.cancel') }}
                                    </button>
                                </div>
                                <div class="pull-right">
                                    <button type="button" id="save-changes-btn" class="btn btn-success btn-flat" disabled>
                                        <i class="fa fa-save"></i> {{ __('messages.action.save') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">{{ __('messages.accounts') }}</h3>
            <div class="box-tools pull-right">
                @if(Auth::user()->can('edit-account', App\Models\School::class))
                <span id="bulk-lock-toggle" class="account-status-indicator" style="cursor: pointer; padding: 8px 10px; border-radius: 4px; display: inline-flex; align-items: center; justify-content: center; background-color: #f0ad4e; color: white;" title="{{ __('messages.bulk_lock_unlock') }}">
                    <i class="fa fa-question-circle" style="font-size: 18px; line-height: 1;"></i>
                </span>
                @endif
            </div>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <table id="tabelapadrao" class="table table-condensed table-bordered table-hover">
                        <thead>
                        <tr>
                            <th style="width: 5%">{{ __('messages.stt') }}</th>
                            <th>{{ __('messages.name') }}</th>
                            <th>{{ __('messages.email') }}</th>
                            <th>{{ __('messages.phone_number') }}</th>
                            <th class="text-center">{{ __('messages.status') }}</th>
                            <th class="text-center" style="width: 10%">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($accounts as $index => $account)
                            <tr>
                                <td>{{ ($accounts->currentPage() - 1) * $accounts->perPage() + $index + 1 }}</td>
                                <td>{{ $account->name }}</td>
                                <td>{{ $account->email }}</td>
                                <td>{{ $account->phone_number }}</td>
                                <td class="text-center">
                                    @if(Auth::user()->can('edit-account', App\Models\Account::class))
                                    <span class="account-lock-toggle account-status-indicator" data-account-id="{{ $account->id }}" data-status="{{ $account->status }}" style="cursor: pointer; padding: 8px 10px; border-radius: 4px; display: inline-flex; align-items: center; justify-content: center; background-color: {{ $account->status == \App\Models\Account::STATUS_ACTIVE ? '#5cb85c' : '#d9534f' }}; color: white;" title="{{ $account->status == \App\Models\Account::STATUS_ACTIVE ? __('messages.status.active') : __('messages.status.locked') }}">
                                        <i class="fa fa-{{ $account->status == \App\Models\Account::STATUS_ACTIVE ? 'unlock' : 'lock' }}" style="font-size: 18px; line-height: 1;"></i>
                                    </span>
                                    @else
                                    <span class="account-status-indicator" style="padding: 8px 10px; border-radius: 4px; display: inline-flex; align-items: center; justify-content: center; background-color: {{ $account->status == \App\Models\Account::STATUS_ACTIVE ? '#5cb85c' : '#d9534f' }}; color: white;" title="{{ $account->status == \App\Models\Account::STATUS_ACTIVE ? __('messages.status.active') : __('messages.status.locked') }}">
                                        <i class="fa fa-{{ $account->status == \App\Models\Account::STATUS_ACTIVE ? 'unlock' : 'lock' }}" style="font-size: 18px; line-height: 1;"></i>
                                    </span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <a class="btn btn-default btn-xs" href="{{ route('account.show', $account->id) }}" title="View {{ $account->name }}"><i class="fa fa-eye"></i></a>
                                    @if(Auth::user()->can('destroy-account', App\Models\Account::class))
                                    <a class="btn btn-danger btn-xs delete-account" href="javascript:void(0);" data-account-id="{{ $account->id }}" data-account-name="{{ $account->name }}" title="Delete {{ $account->name }}"><i class="fa fa-trash"></i></a>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                    <div class="col-md-12 text-center">
                        {{ $accounts->appends(request()->input())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmationModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="deleteConfirmationModalLabel">
                        <i class="fa fa-exclamation-triangle text-warning"></i> {{ __('messages.warning') }}
                    </h4>
                </div>
                <div class="modal-body">
                    <p id="deleteConfirmationMessage"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ __('messages.cancel') }}</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteButton">{{ __('messages.delete') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Confirmation Modal -->
    <div class="modal fade" id="saveConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="saveConfirmationModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="saveConfirmationModalLabel">
                        <i class="fa fa-exclamation-triangle text-warning"></i> {{ __('messages.warning') }}
                    </h4>
                </div>
                <div class="modal-body">
                    <p>{{ __('messages.confirm_save_changes') }}</p>
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> {{ __('messages.accounts_auto_update_notice') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ __('messages.cancel') }}</button>
                    <button type="button" class="btn btn-success" id="confirmSaveButton">{{ __('messages.action.save') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('layout_js')
    <script>
        // Store original values for all fields to support cancel functionality
        const originalValues = {};

        // Store original account statuses
        const originalAccountStatuses = {};

        $(function(){
            // Initialize select2
            $('.select2').select2({
                "language": {
                    "noResults": function(){
                        return "Nenhum registro encontrado.";
                    }
                }
            });

            // Add security check to prevent unauthorized field manipulation
            @if(!Auth::user()->can('edit-account', App\Models\School::class))
            // Disable all editable fields for unauthorized users
            $('.editable-field').prop('disabled', true);

            // Prevent any attempts to enable fields
            $('.editable-field').on('mousedown keydown', function(e) {
                if ($(this).prop('disabled') === false) {
                    e.preventDefault();
                    $(this).prop('disabled', true);
                    alert('{{ __("messages.no_permission_edit_school") }}');
                }
            });
            @endif

            // Store original values for all editable fields
            $('.editable-field').each(function() {
                const field = $(this);
                const name = field.attr('name');

                // Special handling for multi-select
                if (field.is('select[multiple]')) {
                    originalValues[name] = $(this).val() || [];
                } else {
                    originalValues[name] = field.val();
                }
            });

            // Edit school button functionality
            $('#edit-school-btn').on('click', function() {
                // Check if user has edit permission
                @if(Auth::user()->can('edit-account', App\Models\School::class))
                // Enable all editable fields
                $('.editable-field').prop('disabled', false);

                // Change all status icons to check icon
                $('.status-icon').removeClass('fa-pencil').addClass('fa-check');

                // Update software type display to ensure related fields are properly enabled
                softwareTypeChange();

                // Disable edit button
                $(this).prop('disabled', true);

                // Enable save and cancel buttons
                $('#save-changes-btn').prop('disabled', false);
                $('#cancel-changes-btn').prop('disabled', false);
                @else
                // Show error message if user doesn't have permission
                alert('{{ __("messages.no_permission_edit_school") }}');
                @endif
            });

            // Add cancel button functionality
            $('#cancel-changes-btn').on('click', function() {
                // Check if user has edit permission
                @if(Auth::user()->can('edit-account', App\Models\School::class))
                // Restore original values for all editable fields
                $('.editable-field').each(function() {
                    const field = $(this);
                    const name = field.attr('name');

                    // Restore original value
                    if (field.is('select[multiple]')) {
                        field.val(originalValues[name]).trigger('change');
                    } else {
                        field.val(originalValues[name]);
                    }

                    // Disable the field
                    field.prop('disabled', true);
                });

                // Reset all status icons to pencil icon
                $('.status-icon').removeClass('fa-check').addClass('fa-pencil');

                // Update software type display
                softwareTypeChange();

                // Remove all account status inputs and bulk action
                $('input[name^="account_statuses"]').remove();
                $('input[name="bulk_action"]').remove();

                // Reset account status toggles to their original state
                $('.account-lock-toggle').each(function() {
                    const accountId = $(this).data('account-id');
                    const originalStatus = originalAccountStatuses[accountId];
                    const toggleElement = $(this);
                    const icon = toggleElement.find('i');

                    // Reset UI to original state
                    if (originalStatus == 1) {
                        toggleElement.css('background-color', '#5cb85c');
                        icon.removeClass('fa-lock').addClass('fa-unlock');
                        toggleElement.attr('title', '{{ __("messages.status.active") }}');
                    } else {
                        toggleElement.css('background-color', '#d9534f');
                        icon.removeClass('fa-unlock').addClass('fa-lock');
                        toggleElement.attr('title', '{{ __("messages.status.locked") }}');
                    }

                    // Reset data attribute
                    toggleElement.data('status', originalStatus);
                });

                // Reset bulk toggle to original state if it was changed
                if (originalBulkStatus !== null) {
                    const bulkToggle = $('#bulk-lock-toggle');

                    // Reset bulk toggle icon based on original status
                    if (originalBulkStatus === 'unlocked') {
                        bulkToggle.css('background-color', '#5cb85c'); // Green for unlocked
                        bulkToggle.find('i').removeClass().addClass('fa fa-unlock');
                        bulkToggle.attr('title', '{{ __("messages.all_accounts_unlocked") }}');
                        bulkToggle.attr('data-status', 'unlocked');
                        bulkToggle.data('status', 'unlocked');
                    } else if (originalBulkStatus === 'locked') {
                        bulkToggle.css('background-color', '#d9534f'); // Red for locked
                        bulkToggle.find('i').removeClass().addClass('fa fa-lock');
                        bulkToggle.attr('title', '{{ __("messages.all_accounts_locked") }}');
                        bulkToggle.attr('data-status', 'locked');
                        bulkToggle.data('status', 'locked');
                    } else if (originalBulkStatus === 'mixed') {
                        bulkToggle.css('background-color', '#f0ad4e'); // Yellow for mixed
                        bulkToggle.find('i').removeClass().addClass('fa fa-random');
                        bulkToggle.attr('title', '{{ __("messages.mixed_account_status") }}');
                        bulkToggle.attr('data-status', 'mixed');
                        bulkToggle.data('status', 'mixed');
                    }

                    console.log('Reset bulk toggle to original state:', originalBulkStatus);

                    // Reset the original bulk status variable
                    originalBulkStatus = null;
                } else {
                    // If no original status was stored, update based on current account states
                    updateBulkStatusIcon();
                }

                // Disable save and cancel buttons
                $('#save-changes-btn').prop('disabled', true);
                $('#cancel-changes-btn').prop('disabled', true);

                // Re-enable edit button
                $('#edit-school-btn').prop('disabled', false);
                @else
                // Show error message if user doesn't have permission
                alert('{{ __("messages.no_permission_edit_school") }}');
                @endif
            });

            // Individual account lock/unlock toggle
            $('.account-lock-toggle').on('click', function() {
                const accountId = $(this).data('account-id');
                const currentStatus = parseInt($(this).data('status'));
                const toggleElement = $(this);
                const icon = toggleElement.find('i');

                // Toggle status locally
                const newStatus = currentStatus == 1 ? 0 : 1;

                console.log('Toggling account', accountId, 'from status', currentStatus, 'to', newStatus);

                // Update UI immediately for better UX
                if (newStatus == 1) {
                    toggleElement.css('background-color', '#5cb85c');
                    icon.removeClass('fa-lock').addClass('fa-unlock');
                    toggleElement.attr('title', '{{ __("messages.status.active") }}');
                } else {
                    toggleElement.css('background-color', '#d9534f');
                    icon.removeClass('fa-unlock').addClass('fa-lock');
                    toggleElement.attr('title', '{{ __("messages.status.locked") }}');
                }

                // Update data attribute - use attr() instead of data() to ensure it's properly updated
                toggleElement.attr('data-status', newStatus);
                // Also update the data cache
                toggleElement.data('status', newStatus);

                // Check if the status has changed from the original
                if (newStatus !== originalAccountStatuses[accountId]) {
                    // We'll handle all account statuses at form submission time
                    // Just enable the save and cancel buttons
                    $('#save-changes-btn').prop('disabled', false);
                    $('#cancel-changes-btn').prop('disabled', false);
                } else {
                    // Check if there are any remaining changes
                    const hasEnabledFields = $('.editable-field').toArray().some(field => !$(field).prop('disabled'));

                    // Check if any account status has changed from original
                    let anyStatusChanged = false;
                    $('.account-lock-toggle').each(function() {
                        const id = $(this).data('account-id');
                        const isUnlocked = $(this).find('i').hasClass('fa-unlock');
                        const currentStatus = isUnlocked ? 1 : 0;
                        if (currentStatus !== originalAccountStatuses[id]) {
                            anyStatusChanged = true;
                        }
                    });

                    // Disable save and cancel buttons if no changes
                    if (!hasEnabledFields && !anyStatusChanged) {
                        $('#save-changes-btn').prop('disabled', true);
                        $('#cancel-changes-btn').prop('disabled', true);
                    }
                }

                // Update the bulk status icon based on current UI state
                updateBulkStatusIcon();
            });

            // Check accounts status and update the bulk toggle icon
            function checkAccountsStatus() {
                $.ajax({
                    url: '/school/{{ $school->id }}/accounts-status',
                    type: 'GET',
                    success: function(response) {
                        if (response.success) {
                            const bulkToggle = $('#bulk-lock-toggle');
                            const icon = bulkToggle.find('i');

                            // Update icon based on status
                            if (response.status === 'all_locked') {
                                bulkToggle.css('background-color', '#d9534f'); // Red for locked
                                icon.removeClass('fa-unlock fa-random fa-question-circle').addClass('fa-lock');
                                bulkToggle.attr('title', '{{ __("messages.all_accounts_locked") }}');
                                bulkToggle.data('status', 'locked');
                            } else if (response.status === 'all_unlocked') {
                                bulkToggle.css('background-color', '#5cb85c'); // Green for unlocked
                                icon.removeClass('fa-lock fa-random fa-question-circle').addClass('fa-unlock');
                                bulkToggle.attr('title', '{{ __("messages.all_accounts_unlocked") }}');
                                bulkToggle.data('status', 'unlocked');
                            } else if (response.status === 'mixed') {
                                bulkToggle.css('background-color', '#f0ad4e'); // Yellow for mixed
                                icon.removeClass('fa-lock fa-unlock fa-question-circle').addClass('fa-random');
                                bulkToggle.attr('title', '{{ __("messages.mixed_account_status") }}');
                                bulkToggle.data('status', 'mixed');
                            } else {
                                bulkToggle.css('background-color', '#f0ad4e'); // Yellow for no accounts
                                icon.removeClass('fa-lock fa-unlock fa-random').addClass('fa-question-circle');
                                bulkToggle.attr('title', '{{ __("messages.no_accounts") }}');
                                bulkToggle.data('status', 'none');
                            }
                        }
                    }
                });
            }

            // Call on page load
            checkAccountsStatus();

            // Store original account statuses
            $('.account-lock-toggle').each(function() {
                const accountId = $(this).data('account-id');
                const status = $(this).data('status');
                originalAccountStatuses[accountId] = status;
            });

            // Handle delete account button click
            $('.delete-account').on('click', function() {
                const accountId = $(this).data('account-id');
                const accountName = $(this).data('account-name');

                // Create and show the delete confirmation modal
                showDeleteConfirmationModal(accountId, accountName);
            });

            // Function to show delete confirmation modal
            function showDeleteConfirmationModal(accountId, accountName) {
                // Set the confirmation message
                $('#deleteConfirmationMessage').text('{{ __("messages.confirm_delete_account") }} ' + accountName);

                // Set up the confirm button action
                $('#confirmDeleteButton').off('click').on('click', function() {
                    window.location.href = '{{ route("account.destroy", "") }}/' + accountId;
                });

                // Show the modal
                $('#deleteConfirmationModal').modal('show');
            }

            // Handle save button click
            $('#save-changes-btn').on('click', function() {
                // Check if user has edit permission
                @if(Auth::user()->can('edit-account', App\Models\School::class))
                // Debug log to check account status inputs before saving
                console.log('Account status inputs before saving:', $('input[name^="account_statuses"]').length);
                $('input[name^="account_statuses"]').each(function() {
                    console.log('Account ID:', $(this).attr('name'), 'Value:', $(this).val());
                });

                // Show the save confirmation modal
                $('#saveConfirmationModal').modal('show');
                @else
                // Show error message if user doesn't have permission
                alert('{{ __("messages.no_permission_edit_school") }}');
                @endif
            });

            // Handle confirm save button click
            $('#confirmSaveButton').on('click', function() {
                // Check if user has edit permission
                @if(Auth::user()->can('edit-account', App\Models\School::class))
                // Hide the modal
                $('#saveConfirmationModal').modal('hide');

                // Re-enable edit button (will be refreshed on page reload after form submission)
                $('#edit-school-btn').prop('disabled', false);

                // IMPORTANT: Create a completely new approach for account status changes
                // Remove any existing account status inputs first
                $('input[name^="account_statuses"]').remove();

                // Check if we need to include account statuses in the form
                // We only need to include them if they differ from the original values
                let anyStatusChanged = false;
                $('.account-lock-toggle').each(function() {
                    const accountId = $(this).data('account-id');
                    // Get the current visual state (what the user sees)
                    const currentStatus = $(this).find('i').hasClass('fa-unlock') ? 1 : 0;

                    // Check if this status has changed from the original
                    if (currentStatus !== originalAccountStatuses[accountId]) {
                        anyStatusChanged = true;
                        // Create a new hidden input for this account
                        $('#school-form').append('<input type="hidden" name="account_statuses[' + accountId + ']" value="' + currentStatus + '">');
                        console.log('Added account status for ID', accountId, 'with value', currentStatus);
                    }
                });

                // Debug log to check account status inputs right before submission
                console.log('Total account status inputs before submission:', $('input[name^="account_statuses"]').length);
                console.log('Any status changed from original:', anyStatusChanged);
                $('input[name^="account_statuses"]').each(function() {
                    console.log('Account ID:', $(this).attr('name'), 'Value:', $(this).val());
                });

                // Force enable all fields before submission to ensure they're included in the form data
                $('.editable-field').prop('disabled', false);

                // Submit the form
                $('#school-form').submit();
                @else
                // Hide the modal
                $('#saveConfirmationModal').modal('hide');

                // Show error message if user doesn't have permission
                alert('{{ __("messages.no_permission_edit_school") }}');
                @endif
            });

            // Function to update bulk status icon based on current UI state
            function updateBulkStatusIcon() {
                const accountToggles = $('.account-lock-toggle');
                if (accountToggles.length === 0) {
                    // No accounts
                    const bulkToggle = $('#bulk-lock-toggle');
                    bulkToggle.css('background-color', '#f0ad4e'); // Yellow for no accounts
                    bulkToggle.find('i').removeClass('fa-lock fa-unlock fa-random').addClass('fa-question-circle');
                    bulkToggle.attr('title', '{{ __("messages.no_accounts") }}');
                    bulkToggle.attr('data-status', 'none');
                    bulkToggle.data('status', 'none');
                    return;
                }

                let allLocked = true;
                let allUnlocked = true;

                accountToggles.each(function() {
                    const status = $(this).data('status');
                    if (status == 1) {
                        allLocked = false;
                    } else {
                        allUnlocked = false;
                    }
                });

                const bulkToggle = $('#bulk-lock-toggle');
                const icon = bulkToggle.find('i');

                if (allLocked) {
                    bulkToggle.css('background-color', '#d9534f'); // Red for locked
                    icon.removeClass('fa-unlock fa-random fa-question-circle').addClass('fa-lock');
                    bulkToggle.attr('title', '{{ __("messages.all_accounts_locked") }}');
                    bulkToggle.attr('data-status', 'locked');
                    bulkToggle.data('status', 'locked');
                } else if (allUnlocked) {
                    bulkToggle.css('background-color', '#5cb85c'); // Green for unlocked
                    icon.removeClass('fa-lock fa-random fa-question-circle').addClass('fa-unlock');
                    bulkToggle.attr('title', '{{ __("messages.all_accounts_unlocked") }}');
                    bulkToggle.attr('data-status', 'unlocked');
                    bulkToggle.data('status', 'unlocked');
                } else {
                    bulkToggle.css('background-color', '#f0ad4e'); // Yellow for mixed
                    icon.removeClass('fa-lock fa-unlock fa-question-circle').addClass('fa-random');
                    bulkToggle.attr('title', '{{ __("messages.mixed_account_status") }}');
                    bulkToggle.attr('data-status', 'mixed');
                    bulkToggle.data('status', 'mixed');
                }
            }

            // Store original bulk status
            let originalBulkStatus = null;

            // Bulk lock/unlock toggle handler
            $('#bulk-lock-toggle').on('click', function() {
                // Get the current status from the data attribute
                const currentStatus = $(this).attr('data-status') || $(this).data('status');
                console.log('Bulk toggle clicked. Current status from attr():', $(this).attr('data-status'), 'from data():', $(this).data('status'));

                // Don't do anything if there are no accounts
                if (currentStatus === 'none') {
                    return;
                }

                // Store original bulk status on first click if not already stored
                if (originalBulkStatus === null) {
                    originalBulkStatus = currentStatus;
                    console.log('Stored original bulk status:', originalBulkStatus);
                }

                // Determine action based on current status
                let newStatus = 0; // Default to lock (status 0)
                if (currentStatus === 'locked') {
                    newStatus = 1; // Unlock (status 1)
                }

                console.log('Bulk toggling visible accounts. Current status:', currentStatus, 'New status:', newStatus);

                // Remove any existing bulk action input
                $('input[name="bulk_action"]').remove();

                // Store the bulk action in a hidden input to be processed on save
                $('#school-form').append('<input type="hidden" name="bulk_action" value="' + (newStatus == 1 ? 'unlock' : 'lock') + '">');

                // Update all visible account toggles in the UI
                $('.account-lock-toggle').each(function() {
                    const accountId = $(this).data('account-id');
                    const toggleElement = $(this);
                    const icon = toggleElement.find('i');

                    // Update UI
                    if (newStatus == 1) {
                        toggleElement.css('background-color', '#5cb85c');
                        icon.removeClass('fa-lock').addClass('fa-unlock');
                        toggleElement.attr('title', '{{ __("messages.status.active") }}');
                    } else {
                        toggleElement.css('background-color', '#d9534f');
                        icon.removeClass('fa-unlock').addClass('fa-lock');
                        toggleElement.attr('title', '{{ __("messages.status.locked") }}');
                    }

                    // Update data attribute - use attr() instead of data() to ensure it's properly updated
                    toggleElement.attr('data-status', newStatus);
                    // Also update the data cache
                    toggleElement.data('status', newStatus);
                });

                // Update bulk toggle icon based on new status
                const bulkToggle = $(this);
                if (newStatus == 1) {
                    bulkToggle.css('background-color', '#5cb85c'); // Green for unlocked
                    bulkToggle.find('i').removeClass().addClass('fa fa-unlock');
                    bulkToggle.attr('title', '{{ __("messages.all_accounts_unlocked") }}');
                    bulkToggle.attr('data-status', 'unlocked');
                    bulkToggle.data('status', 'unlocked');
                } else {
                    bulkToggle.css('background-color', '#d9534f'); // Red for locked
                    bulkToggle.find('i').removeClass().addClass('fa fa-lock');
                    bulkToggle.attr('title', '{{ __("messages.all_accounts_locked") }}');
                    bulkToggle.attr('data-status', 'locked');
                    bulkToggle.data('status', 'locked');
                }

                // Check if we've returned to the original state
                const currentBulkStatus = bulkToggle.attr('data-status');
                const hasChangedFromOriginal = (currentBulkStatus !== originalBulkStatus);
                console.log('Current bulk status:', currentBulkStatus, 'Original:', originalBulkStatus, 'Has changed:', hasChangedFromOriginal);

                // Check if there are any other changes
                const hasEnabledFields = $('.editable-field').toArray().some(field => !$(field).prop('disabled'));

                // Enable/disable save and cancel buttons based on whether there are changes
                if (hasChangedFromOriginal || hasEnabledFields) {
                    $('#save-changes-btn').prop('disabled', false);
                    $('#cancel-changes-btn').prop('disabled', false);
                    console.log('Enabling save/cancel buttons due to changes');
                } else {
                    $('#save-changes-btn').prop('disabled', true);
                    $('#cancel-changes-btn').prop('disabled', true);
                    console.log('Disabling save/cancel buttons due to no changes');
                    // Remove bulk action input if we're back to original state
                    $('input[name="bulk_action"]').remove();
                }

                console.log('Bulk action set, save button updated. Changes will be applied when save is clicked.');
            });

            // No individual field edit functionality - all fields are edited at once with the edit button

            // Software type change
            $('#software-type').on('change', function() {
                softwareTypeChange();
            });

            // Province change
            $('#province').on('change', function(e){
                var provinceId = $(this).val();
                if(!provinceId){
                    $('#district').empty();
                    $('#district').append('<option value="">{{ __('messages.select') }}</option>');
                    return;
                }

                $.ajax({
                    url: "{{ route('api.districts') }}",
                    type: "GET",
                    data: {
                        province_id: provinceId
                    },
                    success: function(data){
                        $('#district').empty();
                        $('#district').append('<option value="">{{ __('messages.select') }}</option>');
                        $.each(data, function(key, value){
                            $('#district').append('<option value="' + key + '">' + value.name + '</option>');
                        });
                    }
                });
            });
        });

        function softwareTypeChange() {
            let softwareTypes = $('#software-type').val();
            let softwareTypeEnabled = !$('#software-type').prop('disabled');

            if (softwareTypes && softwareTypes.includes('{{ \App\Models\Account::SOFTWARE_MAMNON }}')) {
                $('#mamnon-account-type').show();
                // Set disabled state based on software type field
                $('[name="mamnon_account_type"]').prop('disabled', !softwareTypeEnabled);
            } else {
                $('#mamnon-account-type').hide();
                // Always disable hidden fields
                $('[name="mamnon_account_type"]').prop('disabled', true);
            }

            if (softwareTypes && softwareTypes.includes('{{ \App\Models\Account::SOFTWARE_TIEUHOC }}')) {
                $('#tieuhoc-account-type').show();
                // Set disabled state based on software type field
                $('[name="tieuhoc_account_type"]').prop('disabled', !softwareTypeEnabled);
            } else {
                $('#tieuhoc-account-type').hide();
                // Always disable hidden fields
                $('[name="tieuhoc_account_type"]').prop('disabled', true);
            }

            // Update save/cancel buttons state
            checkSaveButtonVisibility();
        }

        function checkSaveButtonVisibility() {
            const hasEnabledFields = $('.editable-field').toArray().some(field => !$(field).prop('disabled'));

            // Check if any account status has changed from original
            let hasStatusChanges = false;
            $('.account-lock-toggle').each(function() {
                const accountId = $(this).data('account-id');
                const isUnlocked = $(this).find('i').hasClass('fa-unlock');
                const currentStatus = isUnlocked ? 1 : 0;
                if (currentStatus !== originalAccountStatuses[accountId]) {
                    hasStatusChanges = true;
                }
            });

            // For debugging
            console.log('Checking save button visibility:');
            console.log('- Has enabled fields:', hasEnabledFields);
            console.log('- Has status changes:', hasStatusChanges);

            // If we're in edit mode (any field is enabled) or any status has changed
            if (hasEnabledFields || hasStatusChanges) {
                $('#edit-school-btn').prop('disabled', true);
                $('#save-changes-btn').prop('disabled', false);
                $('#cancel-changes-btn').prop('disabled', false);
                console.log('Enabling save/cancel buttons due to changes');
            } else {
                // If no fields are enabled and no status changes, reset all buttons
                $('#edit-school-btn').prop('disabled', false);
                $('#save-changes-btn').prop('disabled', true);
                $('#cancel-changes-btn').prop('disabled', true);
                console.log('Disabling save/cancel buttons due to no changes');
            }
        }
    </script>
@endsection
