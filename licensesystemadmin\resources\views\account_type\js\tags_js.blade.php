<script>
    $(document).ready(function () {
        // Handle Enter key press in the lesson name input
        let mainInput = document.getElementById("lesson-name");
        mainInput.addEventListener('keydown', function (e) {
            let keyCode = e.which || e.keyCode;
            if (keyCode === 13) {
                e.preventDefault(); // Prevent form submission
                if (mainInput.value.length > 0) {
                    addTag();
                }
            }
        });

        // Initialize existing tags
        initializeExistingTags();

        // Focus on the name field when the page loads
        $('#name').focus();
    });

    /**
     * Initialize event handlers for existing tags
     */
    function initializeExistingTags() {
        let existingTags = document.querySelectorAll('.tags-input .tag');
        existingTags.forEach(function (tag) {
            let closeBtn = tag.querySelector('.close');
            if (closeBtn) {
                closeBtn.addEventListener('click', function () {
                    removeTag(tag);
                });
            }
        });
    }

    /**
     * Get all tags from the tags container
     */
    function getTagsFromElement() {
        let elementTags = document.querySelectorAll('.tags-input .tag');
        let tags = [];

        if (elementTags == null) {
            return tags;
        }

        for (let i = 0; i < elementTags.length; i++) {
            let elementTag = elementTags[i];
            // Get text content without the "×" close button
            let text = elementTag.textContent.replace('×', '').trim();
            tags.push(text);
        }

        return tags;
    }

    /**
     * Add a new tag or multiple tags (comma-separated)
     */
    function addTag() {
        let el = document.getElementsByClassName('tags-input')[0];
        let text = $('#lesson-name').val().trim();

        if (text) {
            // Split by comma to allow adding multiple tags at once
            let lessons = text.split(',').map(lesson => lesson.trim());

            lessons.forEach(function (lesson) {
                if (lesson) {
                    // Create the tag element
                    let tag = document.createElement('span');
                    tag.classList.add('tag');
                    tag.textContent = lesson;

                    // Create the close button
                    let closeBtn = document.createElement('span');
                    closeBtn.classList.add('close');
                    closeBtn.addEventListener('click', function () {
                        removeTag(tag);
                    });
                    tag.appendChild(closeBtn);

                    // Add the tag to the container
                    el.appendChild(tag);

                    // Add a subtle animation effect
                    $(tag).hide().fadeIn(300);
                }
            });

            // Update the hidden input and clear the text field
            refreshTags();

            // Focus back on the input for better UX
            $('#lesson-name').focus();
        }
    }

    /**
     * Remove a tag
     */
    function removeTag(tagElement) {
        // Add a fade-out animation before removing
        $(tagElement).fadeOut(200, function() {
            $(this).remove();
            refreshTags();
        });
    }

    /**
     * Update the hidden input with all tags
     */
    function refreshTags() {
        let tags = getTagsFromElement();
        let strTags = tags.join(',');
        $('#lesson-list').val(strTags);
        $('#lesson-name').val('');
    }

    /**
     * Filter tag text to remove special characters
     * (Currently not used but kept for potential future use)
     */
    function filterTag(tag) {
        return tag.replace(/[^\w -]/g, '').trim().replace(/\W+/g, '-');
    }
</script>