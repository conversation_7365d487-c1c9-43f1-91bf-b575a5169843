@if(isset($accountType->paginatedMaterials))
    @foreach($accountType->paginatedMaterials as $material)
        @php
            // Try to match by material ID (bin-01, bin-02, etc.)
            $materialId = null;
            $exists = false;

            // Check if the material exists in our database
            if (array_key_exists($material, $existingMaterials)) {
                $exists = true;
                $materialId = $existingMaterials[$material];
            }

            // If not found and it looks like "bin-XX", try to extract the number
            if (!$exists && preg_match('/bin-(\d+)/', $material, $matches)) {
                $materialNumber = $matches[1];
                // Remove leading zeros
                $materialNumber = ltrim($materialNumber, '0');

                // Try with just the number
                if (array_key_exists($materialNumber, $existingMaterials)) {
                    $exists = true;
                    $materialId = $existingMaterials[$materialNumber];
                }

                // Try with "Bài học XX" format
                $baiHocFormat = 'Bài học ' . $materialNumber;
                if (!$exists && array_key_exists($baiHocFormat, $existingMaterials)) {
                    $exists = true;
                    $materialId = $existingMaterials[$baiHocFormat];
                }

                // Try with "1.XX" format
                $dotFormat = '1.' . $materialNumber;
                if (!$exists && array_key_exists($dotFormat, $existingMaterials)) {
                    $exists = true;
                    $materialId = $existingMaterials[$dotFormat];
                }
            }

            $cssClass = $exists ? 'exists' : 'not-exists';
        @endphp
        <a href="{{ $exists ? route('material.edit', $materialId) : route('material.create') . '?content=' . urlencode($material) }}"
           class="tag material-tag {{ $cssClass }}"
           data-material="{{ $material }}"
           @if($exists) data-material-id="{{ $materialId }}" @endif
           title="{{ $exists ? 'Edit material' : 'Create new material' }}">
            {{ $material }}
        </a>
    @endforeach

    <div class="pagination-container" id="pagination-container-{{ $accountType->id }}">
        <div class="pagination-wrapper">
            @php
                $paginator = $accountType->paginatedMaterials;
                $totalPages = $paginator->lastPage();
                $currentPage = $paginator->currentPage();
            @endphp

            <ul class="pagination">
                {{-- Previous Page Link --}}
                @if ($currentPage > 1)
                    <li>
                        <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $currentPage - 1 }}">&laquo;</a>
                    </li>
                @else
                    <li class="disabled">
                        <span>&laquo;</span>
                    </li>
                @endif

                {{-- Page Number Links --}}
                @for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++)
                    <li class="{{ $i == $currentPage ? 'active' : '' }}">
                        <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $i }}">{{ $i }}</a>
                    </li>
                @endfor

                {{-- Next Page Link --}}
                @if ($currentPage < $totalPages)
                    <li>
                        <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $currentPage + 1 }}">&raquo;</a>
                    </li>
                @else
                    <li class="disabled">
                        <span>&raquo;</span>
                    </li>
                @endif
            </ul>

        </div>
        <div class="pagination-info">
            Showing {{ $paginator->firstItem() }} to {{ $paginator->lastItem() }} of {{ $paginator->total() }} materials
            @if($accountType->nonExistentCount > 0)
                <span class="non-existent-info">
                    ({{ $accountType->nonExistentCount }} {{ __('messages.non_existent_materials') }})
                </span>
            @endif
        </div>
    </div>
@else
    <p>No materials found.</p>
@endif
