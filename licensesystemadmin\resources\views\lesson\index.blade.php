@extends('layouts.AdminLTE.index')

@section('icon_page', 'user')

@section('title', __('messages.index.lesson'))

@section('menu_pagina')

<li role="presentation">
    <a href="{{ route('lesson.create') }}" class="link_menu_page">
        <i class="fa fa-plus"></i> {{ __('messages.add.lesson') }}
    </a>
</li>

@endsection

@section('content')

<div class="box box-primary">
    <div class="box-body">
        <div class="row">
            <form action="{{ route('lesson') }}" method="get">
                <div class="col-lg-3">
                    <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                        <label for="nome">{{ __('messages.search') }}</label>
                        <input type="text" name="search" class="form-control" placeholder="{{ __('messages.search') }}" value="{{ $keyword }}">
                    </div>
                </div>
                <div class="col-lg-1">
                    <label for="nome">{{ __('messages.filter') }} / {{ __('messages.search') }}</label>
                    <button type="submit" class="btn btn-google pull-right"><i class="fa fa-fw fa-search"></i> {{ __('messages.search') }}</button>
                </div>

            </form>
            <!-- modal -->

        </div>
    </div>
    <div class="box-body">
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive">
                    <table id="tabelapadrao" class="table table-condensed table-bordered table-hover">
                        <thead>
                        <tr>
                            <th></th>
                            <th class="text-center col-md-3">{{ __('messages.title') }}</th>
                            <th class="text-center col-md-3">{{ __('messages.course') }}</th>
                            <th class="text-center col-md-2">{{ __('messages.created_at') }}</th>
                            <th class="text-center">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php $cnt = 1; @endphp
                        @foreach($lessons as $lesson)
                        <tr>
                            <td>{{ $cnt++ }}</td>
                            <td class="text-center">{{ $lesson->title }}</td>
                            <td class="text-center">{{ $lesson->course->title_en }}</td>
                            <td class="text-center">{{$lesson->created_at->format('d/m/Y H:i')}}</td>

                            <td class="text-center">
                                <a class="btn btn-warning  btn-xs" href="{{ route('lesson.edit', $lesson->id) }}" title="Edit {{ $lesson->title }}"><i class="fa fa-pencil"></i></a>
                                <a class="btn btn-danger  btn-xs" href="#" title="Delete {{ $lesson->name}}" data-toggle="modal" data-target="#modal-delete-{{ $lesson->id }}"><i class="fa fa-trash"></i></a>
                            </td>
                        </tr>
                        <div class="modal fade" id="modal-delete-{{ $lesson->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>{{ __('messages.do_you_want_delete') }} ({{ $lesson->title }}) ?</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cancel</button>
                                        <a href="{{ route('lesson.destroy', $lesson->id) }}"><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-12 text-center">
                {{ $lessons->links() }}
            </div>
        </div>
    </div>
</div>

<script>
    $('#province').on('change', function(e){
        e.preventDefault();
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        let province = $('#province').val();
        let districtDOM = $('#district');
        $.ajax({
            type:'GET',
            url: "/api/v1/provinces/{id}/districts".replace("{id}", province),
            success:function(response){
                $('option', districtDOM).remove();
                $('#district').append('<option value="-1">{{ __('messages.all') }}</option>');
                $.each(response.data, function(){
                    $('<option/>', {
                        'value': this.code,
                        'text': this.name
                    }).appendTo('#district');
                });
            }

        });
    });

    $(document).ready(function () {
        $('#checkbox_master').on('click', function(e) {
            if($(this).is(':checked',true))
            {
                $(".checkbox_row").prop('checked', true);
            } else {
                $(".checkbox_row").prop('checked',false);
            }

            let allVals = [];
            $(".checkbox_row:checked").each(function() {
                allVals.push($(this).attr('data-id'));
            });

            $('#bulk_destroy_value').val("");
            $('#bulk_destroy_value').val(allVals.join(','));

            $('#bulk_locked_value').val("");
            $('#bulk_locked_value').val(allVals.join(','));

            $('#bulk_extend_expired_value').val("");
            $('#bulk_extend_expired_value').val(allVals.join(','));
        });

        $('.checkbox_row').on('click', function(e) {
            let allVals = [];
            $(".checkbox_row:checked").each(function() {
                allVals.push($(this).attr('data-id'));
            });

            $('#bulk_destroy_value').val("");
            $('#bulk_destroy_value').val(allVals.join(','));

            $('#bulk_locked_value').val("");
            $('#bulk_locked_value').val(allVals.join(','));

            $('#bulk_extend_expired_value').val("");
            $('#bulk_extend_expired_value').val(allVals.join(','));
        });
    });
</script>

@endsection
