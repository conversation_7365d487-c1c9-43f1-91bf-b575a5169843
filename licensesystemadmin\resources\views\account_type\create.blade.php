@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.add.account_type'))

@section('menu_pagina')
    <li role="presentation">
        <a href="{{ route('account_type') }}" class="link_menu_page">
            <i class="fa fa-unlock-alt"></i> {{ __('messages.index.account_type') }}
        </a>
    </li>
@endsection

@section('layout_css')
<style>
    .form-section {
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .form-section-title {
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
        margin-bottom: 15px;
        font-weight: 600;
        color: #3c8dbc;
    }

    .form-group label {
        font-weight: 600;
        color: #555;
    }

    .form-control {
        height: 38px;
        border-radius: 3px;
    }

    .form-control:focus {
        border-color: #3c8dbc;
        box-shadow: 0 0 5px rgba(60, 141, 188, 0.3);
    }

    .tags-input {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 10px;
        border: 1px solid #d2d6de;
        border-radius: 3px;
        background-color: #fff;
        min-height: 100px;
        margin-top: 10px;
    }

    .tags-input .tag {
        font-size: 14px;
        padding: 6px 12px;
        margin: 0;
        display: inline-flex;
        align-items: center;
        background-color: #3c8dbc;
        color: white;
        border-radius: 20px;
        transition: all 0.2s ease;
    }

    .tags-input .tag:hover {
        background-color: #367fa9;
    }

    .tags-input .tag .close {
        margin-left: 8px;
        font-size: 16px;
        opacity: 0.8;
        cursor: pointer;
    }

    .tags-input .tag .close::after {
        content: '×';
        font-weight: bold;
    }

    .tags-input .tag .close:hover::after {
        color: #f8f8f8;
    }

    .input-group-btn {
        vertical-align: top;
    }

    .help-text {
        color: #777;
        font-size: 12px;
        margin-top: 5px;
    }

    .btn-add-tag {
        height: 38px;
    }

    .select2-container--default .select2-selection--multiple {
        min-height: 38px;
        border-color: #d2d6de;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background-color: #3c8dbc;
        border-color: #367fa9;
        color: white;
        padding: 3px 8px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        color: #fff;
        margin-right: 5px;
    }

    .action-buttons {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
</style>
@endsection

@section('content')
    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('account_type.store') }}" method="post">
                        {{ csrf_field() }}
                        <input type="hidden" name="active" value="1">

                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h4 class="form-section-title"><i class="fa fa-info-circle"></i> {{ __('messages.basic_information') }}</h4>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                                        <label for="name">{{ __('messages.name') }}</label>
                                        <input type="text" name="name" id="name" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.name') }}" required autofocus value="{{ old('name') }}">
                                        @if($errors->has('name'))
                                            <span class="help-block">
                                                <strong>{{ $errors->first('name') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group {{ $errors->has('code') ? 'has-error' : '' }}">
                                        <label for="code">{{ __('messages.code') }}</label>
                                        <input type="text" name="code" id="code" class="form-control" placeholder="{{ __('messages.code') }}" value="{{ old('code') }}">
                                        @if($errors->has('code'))
                                            <span class="help-block">
                                                <strong>{{ $errors->first('code') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group {{ $errors->has('description') ? 'has-error' : '' }}">
                                        <label for="description">{{ __('messages.description') }}</label>
                                        <textarea name="description" id="description" class="form-control" rows="3" placeholder="{{ __('messages.description') }}">{{ old('description') }}</textarea>
                                        @if($errors->has('description'))
                                            <span class="help-block">
                                                <strong>{{ $errors->first('description') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Materials Section -->
                        <div class="form-section">
                            <h4 class="form-section-title"><i class="fa fa-book"></i> {{ __('messages.lesson_list') }}</h4>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group {{ $errors->has('lesson_list') ? 'has-error' : '' }}">
                                        <label for="lesson-name">{{ __('messages.lesson_list') }}</label>
                                        <p class="help-text"><i class="fa fa-info-circle"></i> Viết các bài học cách nhau bởi dấu phẩy. Ví dụ: 1,2,4,5,..</p>

                                        <input type="hidden" name="lesson_list" id="lesson-list" value="{{ old('lesson_list') }}">

                                        <div class="input-group">
                                            <input type="text" class="form-control" id="lesson-name" placeholder="{{ __('messages.enter_lesson_name') }}">
                                            <span class="input-group-btn">
                                                <button type="button" class="btn btn-primary btn-add-tag" onclick="addTag()">
                                                    <i class="fa fa-plus"></i> {{ __('messages.add') }}
                                                </button>
                                            </span>
                                        </div>

                                        <div class="tags-input" data-name="tags-input">
                                            <!-- Tags will be added here dynamically -->
                                        </div>

                                        @if($errors->has('lesson_list'))
                                            <span class="help-block">
                                                <strong>{{ $errors->first('lesson_list') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Courses Section -->
                        <div class="form-section">
                            <h4 class="form-section-title"><i class="fa fa-graduation-cap"></i> {{ __('messages.courses') }}</h4>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group {{ $errors->has('courses') ? 'has-error' : '' }}">
                                        <label for="courses">{{ __('messages.courses') }}</label>
                                        <select name="courses[]" class="form-control select2" id="courses" multiple="multiple" data-placeholder="{{ __('messages.courses') }}" required>
                                            @foreach($courses as $course)
                                                <option value="{{ $course->id }}"> {{ $course->title_en . ' - ' . $course->description_en }} </option>
                                            @endforeach
                                        </select>
                                        @if($errors->has('courses'))
                                            <span class="help-block">
                                                <strong>{{ $errors->first('courses') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons text-right">
                            <a href="{{ route('account_type') }}" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> {{ __('messages.action.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-plus"></i> {{ __('messages.action.add') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

    <script>
        $(function(){
            $('.select2').select2({
                "language": {
                    "noResults": function(){
                        return "Nenhum registro encontrado.";
                    }
                }
            });
        });

        $(function(){
            $('.icheck').iCheck({
              checkboxClass: 'icheckbox_square-blue',
              radioClass: 'iradio_square-blue'
            });
        });
    </script>
    @include('account_type.js.tags_js')

@endsection
