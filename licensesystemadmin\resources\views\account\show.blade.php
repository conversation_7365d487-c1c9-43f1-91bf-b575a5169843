@extends('layouts.AdminLTE.index')

@section('icon_page', 'eye')

@section('title', __('messages.detail.account.game'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('account') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.account.game') }}
        </a>
    </li>

@endsection

@section('content')
    <style>
        .edit-toggle {
            cursor: pointer;
            margin-left: 5px;
            color: #3c8dbc;
            transition: color 0.3s;
        }
        .edit-toggle:hover {
            color: #72afd2;
        }
        .status-toggle {
            cursor: pointer;
        }
        .status-icon {
            cursor: pointer;
            margin-left: 5px;
            color: #3c8dbc;
            transition: color 0.3s;
        }
        .status-icon:hover {
            color: #72afd2;
        }
        .editable-field:disabled {
            background-color: #f9f9f9;
            cursor: default;
        }
        .editable-field:not(:disabled) {
            background-color: #fff;
            border-color: #3c8dbc;
        }
        .panel {
            border-radius: 3px;
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .panel-heading {
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
            padding: 10px 15px;
            background-color: #f5f5f5;
        }
        .panel-title {
            margin-top: 0;
            margin-bottom: 0;
            font-size: 16px;
            color: #333;
        }
        .panel-body {
            padding: 15px;
        }
        label {
            font-weight: 600;
            margin-bottom: 5px;
            display: block;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-control {
            border-radius: 3px;
        }
        .select2-container--default .select2-selection--single,
        .select2-container--default .select2-selection--multiple {
            border-radius: 3px;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }

        .field-being-edited {
            transition: all 0.3s ease;
        }
    </style>
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">{{ __('messages.account_info') }}</h3>
            <div class="box-tools pull-right">
                @if(Auth::user()->can('edit-account', App\Models\Account::class))
                <button type="button" id="edit-account-btn" class="btn btn-sm btn-primary" title="{{ __('messages.edit_account') }}">
                    <i class="fa fa-pencil"></i> {{ __('messages.edit') }}
                </button>
                @endif
            </div>
        </div>
        <div class="box-body">
            <form id="account-form" action="{{ route('account.update', ['id' => $account->id]) }}" method="post">
                {{ csrf_field() }}
                <input type="hidden" name="_method" value="put">
                @if(!Auth::user()->can('edit-account', App\Models\Account::class))
                <input type="hidden" name="no_permission" value="true">
                @endif
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-lg-6">
                                <!-- Basic Information -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h3 class="panel-title">{{ __('messages.basic_info') }}</h3>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>{{ __('messages.name') }} @if(Auth::user()->can('edit-account', App\Models\Account::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                                    <input type="text" name="name" class="form-control editable-field" value="{{ $account->name }}" disabled>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>{{ __('messages.name_org') }} @if(Auth::user()->can('edit-account', App\Models\Account::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                                    <input type="text" name="name_org" class="form-control editable-field" value="{{ $account->name_org }}" disabled>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>{{ __('messages.email') }} @if(Auth::user()->can('edit-account', App\Models\Account::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                                    <input type="email" name="email" class="form-control editable-field" value="{{ $account->email }}" disabled>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>{{ __('messages.phone_number') }} @if(Auth::user()->can('edit-account', App\Models\Account::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                                    <input type="text" name="phone_number" class="form-control editable-field" value="{{ $account->phone_number }}" disabled>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>{{ __('messages.class') }} @if(Auth::user()->can('edit-account', App\Models\Account::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                                    <input type="text" name="class" class="form-control editable-field" value="{{ $account->class }}" disabled>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>{{ __('messages.status') }}</label>
                                                    <div>
                                                        <div id="status-toggle" class="status-toggle" data-account-id="{{ $account->id }}" data-original-status="{{ $account->status }}">
                                                            @if($account->status == 1)
                                                            <span class="label label-success" style="padding: 8px 10px; border-radius: 4px; display: inline-flex; align-items: center; justify-content: center; @if(Auth::user()->can('edit-account', App\Models\Account::class)) cursor: pointer; @endif" title="{{ __('messages.status.active') }}"><i class="fa fa-unlock" style="font-size: 18px; line-height: 1;"></i></span>
                                                            @else
                                                            <span class="label label-danger" style="padding: 8px 10px; border-radius: 4px; display: inline-flex; align-items: center; justify-content: center; @if(Auth::user()->can('edit-account', App\Models\Account::class)) cursor: pointer; @endif" title="{{ __('messages.status.locked') }}"><i class="fa fa-lock" style="font-size: 18px; line-height: 1;"></i></span>
                                                            @endif
                                                        </div>
                                                        <input type="hidden" name="status" id="status-input" value="{{ $account->status }}">
                                                        <!-- Add hidden inputs for software types -->
                                                        <input type="hidden" name="software_types[]" value="{{ \App\Models\Account::SOFTWARE_MAMNON }}" {{ $account->is_mamnon != 1 ? 'disabled' : '' }}>
                                                        <input type="hidden" name="software_types[]" value="{{ \App\Models\Account::SOFTWARE_TIEUHOC }}" {{ $account->is_tieuhoc != 1 ? 'disabled' : '' }}>
                                                        @if(!Auth::user()->can('edit-account', App\Models\Account::class))
                                                        <small class="text-muted">{{ __('messages.no_permission_change_status') }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- School Information -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h3 class="panel-title">{{ __('messages.school_info') }}</h3>
                                    </div>
                                    <div class="panel-body">
                                        <!-- Apply the same styling to this panel -->
                                        <style>
                                            .panel-body p {
                                                margin-bottom: 0;
                                            }
                                        </style>
                                        @if($account->school)
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>{{ __('messages.school_name') }}</label>
                                                        <p>{{ $account->school->name }}</p>
                                                    </div>

                                                    <div class="form-group">
                                                        <label>{{ __('messages.software_type') }}</label>
                                                        <p>
                                                            @if($account->school->is_mamnon == 1)
                                                                {{ __('messages.software_mamnon') }}
                                                                @if($account->school->is_tieuhoc == 1)
                                                                    , {{ __('messages.software_tieuhoc') }}
                                                                @endif
                                                            @elseif($account->school->is_tieuhoc == 1)
                                                                {{ __('messages.software_tieuhoc') }}
                                                            @endif
                                                        </p>
                                                    </div>

                                                    @if($account->school->is_mamnon == 1 && $account->school->mamnonAccountType)
                                                    <div class="form-group">
                                                        <label>{{ __('messages.mamnon_account_type') }}</label>
                                                        <p>{{ $account->school->mamnonAccountType->name }}</p>
                                                    </div>
                                                    @endif

                                                    @if($account->school->is_tieuhoc == 1 && $account->school->tieuhocAccountType)
                                                    <div class="form-group">
                                                        <label>{{ __('messages.tieuhoc_account_type') }}</label>
                                                        <p>{{ $account->school->tieuhocAccountType->name }}</p>
                                                    </div>
                                                    @endif
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>{{ __('messages.time_expired') }}</label>
                                                        <p>{{ $account->school->time_expired != null ? \Carbon\Carbon::parse($account->school->time_expired)->format('d-m-Y') : __('messages.not_set') }}</p>
                                                    </div>

                                                    <div class="form-group">
                                                        <label>{{ __('messages.province') }}</label>
                                                        <p>{{ $account->school->province != null && array_key_exists($account->school->province, $provinces) ? $provinces[$account->school->province . '']['name'] : __('messages.not_set') }}</p>
                                                    </div>

                                                    <div class="form-group">
                                                        <label>{{ __('messages.district') }}</label>
                                                        <p>{{ $account->school->district != null && array_key_exists($account->school->district, $districts) ? $districts[$account->school->district . '']['name'] : __('messages.not_set') }}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <a href="{{ route('school.show', $account->school->id) }}" class="btn btn-primary">
                                                    <i class="fa fa-eye"></i> {{ __('messages.view_school') }}
                                                </a>
                                            </div>
                                        @else
                                            <div class="form-group">
                                                <p>{{ __('messages.no_school_assigned') }}</p>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Account Settings -->
                            <div class="col-lg-6">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h3 class="panel-title">{{ __('messages.account_settings') }}</h3>
                                    </div>
                                    <div class="panel-body">
                                        <style>
                                            .panel-body h4 {
                                                margin-top: 5px;
                                                margin-bottom: 5px;
                                                font-size: 16px;
                                            }
                                            .panel-body hr {
                                                margin-top: 10px;
                                                margin-bottom: 10px;
                                            }
                                            .panel-body .form-group {
                                                margin-bottom: 8px;
                                            }
                                        </style>

                                        <!-- Location Information -->
                                        <h4>{{ __('messages.location_info') }}</h4>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>{{ __('messages.province') }} @if(Auth::user()->can('edit-account', App\Models\Account::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                                    <select name="province" id="province" class="form-control select2 editable-field school-synced-field" disabled>
                                                        @foreach($provinces as $key => $province)
                                                            <option value="{{ $key }}" @if($account->province == $key) selected @endif> {{ $province['name'] }} </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>{{ __('messages.district') }} @if(Auth::user()->can('edit-account', App\Models\Account::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                                    <select name="district" id="district" class="form-control select2 editable-field school-synced-field" disabled>
                                                        @foreach($districts as $key => $district)
                                                            <option value="{{ $key }}" @if($account->district == $key) selected @endif> {{ $district['name'] }} </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <hr>

                                        <!-- Software Information -->
                                        <h4>{{ __('messages.software_info') }}</h4>
                                        <div class="form-group">
                                            <label>{{ __('messages.software_type') }}</label>
                                            <p>
                                                @if($account->is_mamnon == 1)
                                                    {{ __('messages.software_mamnon') }}
                                                    @if($account->is_tieuhoc == 1)
                                                        , {{ __('messages.software_tieuhoc') }}
                                                    @endif
                                                @elseif($account->is_tieuhoc == 1)
                                                    {{ __('messages.software_tieuhoc') }}
                                                @endif
                                            </p>
                                        </div>

                                        @if($account->is_mamnon == 1 && $account->mamnonAccountType)
                                            <div class="form-group">
                                                <label>{{ __('messages.mamnon_account_type') }}</label>
                                                <p>{{ $account->mamnonAccountType->name }}</p>
                                            </div>
                                        @endif

                                        @if($account->is_tieuhoc == 1 && $account->tieuhocAccountType)
                                            <div class="form-group">
                                                <label>{{ __('messages.tieuhoc_account_type') }}</label>
                                                <p>{{ $account->tieuhocAccountType->name }}</p>
                                            </div>
                                        @endif

                                        <hr>

                                        <!-- Time Information -->
                                        <h4>{{ __('messages.time_info') }}</h4>
                                        <div class="form-group">
                                            <label>{{ __('messages.time_expired') }} @if(Auth::user()->can('edit-account', App\Models\Account::class))<i class="fa fa-pencil status-icon"></i>@endif</label>
                                            <div class="input-group">
                                                <input type="datetime-local" name="time_expired" class="form-control editable-field" value="{{ $account->time_expired != null ? \Carbon\Carbon::parse($account->time_expired)->format('Y-m-d\TH:i') : '' }}" disabled>
                                                @if(Auth::user()->can('edit-account', App\Models\Account::class))
                                                <span class="input-group-btn">
                                                    <a href="{{ route('account.extend_time_expired', ['id' => $account->id]) }}" class="btn btn-default" title="{{ __('messages.extend_time_expired') }}">
                                                        <i class="fa fa-calendar-plus-o"></i>
                                                    </a>
                                                </span>
                                                @endif
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label>{{ __('messages.created_at') }}</label>
                                            <p>{{ $account->created_at->format('d/m/Y H:i') }}</p>
                                        </div>

                                        <div class="form-group">
                                            <label>{{ __('messages.last_updated_time') }}</label>
                                            <p>{{ $account->updated_at->format('d/m/Y H:i') }}</p>
                                        </div>

                                        @if($account->school_id && Auth::user()->can('edit-account', App\Models\Account::class))
                                            <hr>
                                            <div class="form-group">
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="sync_with_school" value="1" id="sync-with-school" class="editable-field" checked>
                                                        {{ __('messages.sync_with_school') }}
                                                    </label>
                                                    <p class="help-block">
                                                        <small>{{ __('messages.sync_with_school_help') }}</small>
                                                    </p>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if(Auth::user()->can('edit-account', App\Models\Account::class))
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="pull-left">
                                    <button type="button" id="cancel-changes-btn" class="btn btn-danger btn-flat" disabled>
                                        <i class="fa fa-times"></i> {{ __('messages.action.cancel') }}
                                    </button>
                                </div>
                                <div class="pull-right">
                                    <button type="submit" id="save-changes-btn" class="btn btn-success btn-flat" disabled>
                                        <i class="fa fa-save"></i> {{ __('messages.action.save') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('layout_js')
<script>
    // Add translations if they don't exist
    if (typeof window.translations === 'undefined') {
        window.translations = {
            'basic_info': '{{ __('messages.basic_info') }}',
            'location_info': '{{ __('messages.location_info') }}',
            'software_info': '{{ __('messages.software_info') }}',
            'time_info': '{{ __('messages.time_info') }}',
            'no_results': '{{ __('messages.no_results') }}'
        };
    }

    // Store original values for all fields to support cancel functionality
    const originalValues = {};

    $(function(){
        // Initialize select2
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return window.translations.no_results || "{{ __('messages.no_results') }}";
                }
            }
        });

        // Log initial sync state
        if ($('#sync-with-school').length > 0 && $('#sync-with-school').is(':checked')) {
            console.log('Account sync with school is enabled by default');
        }

        // Store original values for all editable fields
        $('.editable-field').each(function() {
            const field = $(this);
            const name = field.attr('name');

            // Special handling for multi-select
            if (field.is('select[multiple]')) {
                originalValues[name] = $(this).val() || [];
            } else {
                originalValues[name] = field.val();
            }
        });

        // Add cancel button functionality
        $('#cancel-changes-btn').on('click', function() {
            // Restore original values for all editable fields
            $('.editable-field').each(function() {
                const field = $(this);
                const name = field.attr('name');

                // Restore original value
                if (field.is('select[multiple]')) {
                    field.val(originalValues[name]).trigger('change');
                } else {
                    field.val(originalValues[name]);
                }

                // Disable the field
                field.prop('disabled', true);
            });

            // Always use the data attribute for the original status
            const dataOriginalStatus = $('#status-toggle').data('original-status');
            $('#status-input').val(dataOriginalStatus);

            // Update status UI
            const statusToggle = $('#status-toggle');
            const statusSpan = statusToggle.find('span');
            const statusIcon = statusToggle.find('i');

            if (dataOriginalStatus == 1) {
                statusSpan.removeClass('label-danger').addClass('label-success');
                statusIcon.removeClass('fa-lock').addClass('fa-unlock');
                statusSpan.attr('title', '{{ __("messages.status.active") }}');
            } else {
                statusSpan.removeClass('label-success').addClass('label-danger');
                statusIcon.removeClass('fa-unlock').addClass('fa-lock');
                statusSpan.attr('title', '{{ __("messages.status.locked") }}');
            }

            console.log('Reset status to original:', dataOriginalStatus);

            // Reset all status icons to pencil icon
            $('.status-icon').removeClass('fa-check').addClass('fa-pencil');

            // Re-enable edit button
            $('#edit-account-btn').prop('disabled', false);

            // Update software type display
            softwareTypeChange();

            // Disable save and cancel buttons
            checkSaveButtonVisibility();
        });

        // Edit account button functionality
        $('#edit-account-btn').on('click', function() {
            // Check if user has edit permission
            @if(Auth::user()->can('edit-account', App\Models\Account::class))
            // Enable all editable fields
            $('.editable-field').prop('disabled', false);

            // Change all status icons to check icon
            $('.status-icon').removeClass('fa-pencil').addClass('fa-check');

            // Check if sync with school is enabled
            if ($('#sync-with-school').length > 0 && $('#sync-with-school').is(':checked')) {
                // Re-lock school-related fields if sync is enabled
                lockSchoolRelatedFields();
                console.log('School-related fields remain locked due to sync with school being enabled');
            }

            // Update software type display to ensure related fields are properly enabled
            softwareTypeChange();

            // Disable edit button
            $(this).prop('disabled', true);

            // Enable save and cancel buttons
            $('#save-changes-btn').prop('disabled', false);
            $('#cancel-changes-btn').prop('disabled', false);

            // Log the action
            console.log('Edit mode enabled, fields are now editable (except those locked by school sync)');
            @else
            alert('{{ __("messages.no_permission_edit_account") ?: "You do not have permission to edit account information" }}');
            @endif
        });

        // We're using the data-original-status attribute directly instead of a variable

        // Status toggle functionality
        $('#status-toggle').on('click', function() {
            // Check if user has edit permission
            @if(Auth::user()->can('edit-account', App\Models\Account::class))
            // Save the current state of the edit button before making any changes
            const wasEditButtonDisabled = $('#edit-account-btn').prop('disabled');

            // Toggle status locally without making an AJAX call
            const statusToggle = $('#status-toggle');
            const statusSpan = statusToggle.find('span');
            const statusIcon = statusToggle.find('i');
            const statusInput = $('#status-input');

            // Get the original status from the data attribute
            const dataOriginalStatus = parseInt(statusToggle.data('original-status'));
            console.log('Original status from data attribute:', dataOriginalStatus);

            // Get current status
            const currentStatus = parseInt(statusInput.val());
            console.log('Current status before toggle:', currentStatus);

            // Toggle status (1 = active, 0 = locked)
            const newStatus = currentStatus === 1 ? 0 : 1;
            console.log('New status after toggle:', newStatus);

            // Update UI
            if (newStatus === 1) {
                statusSpan.removeClass('label-danger').addClass('label-success');
                statusIcon.removeClass('fa-lock').addClass('fa-unlock');
                statusSpan.attr('title', '{{ __("messages.status.active") }}');
            } else {
                statusSpan.removeClass('label-success').addClass('label-danger');
                statusIcon.removeClass('fa-unlock').addClass('fa-lock');
                statusSpan.attr('title', '{{ __("messages.status.locked") }}');
            }

            // Update the hidden input - force it to be a string to avoid any type issues
            statusInput.val(String(newStatus));
            console.log('Status input value after update:', statusInput.val());

            // Log status change to console
            console.log('Status changed to: ' + newStatus + ' (' + (newStatus === 1 ? 'Active' : 'Locked') + ')');

            // Check if the status has changed from the original data attribute value
            // This is the key fix - we compare with the original data attribute value
            const hasStatusChanged = (newStatus !== dataOriginalStatus);
            console.log('Has status changed from original:', hasStatusChanged);

            // Force a call to checkSaveButtonVisibility to ensure buttons are updated correctly
            // This is critical for handling repeated clicks on the status toggle
            // We use setTimeout to ensure this runs after the current function completes
            setTimeout(function() {
                // Re-check if the status has changed from the original
                const currentStatusAfterUpdate = parseInt($('#status-input').val());
                const originalStatusFromData = parseInt($('#status-toggle').data('original-status'));
                const hasStatusChangedAfterUpdate = currentStatusAfterUpdate !== originalStatusFromData;

                console.log('Re-checking status after update:', {
                    currentStatusAfterUpdate,
                    originalStatusFromData,
                    hasStatusChangedAfterUpdate
                });

                // Update save/cancel buttons based on whether there are any changes
                if (hasStatusChangedAfterUpdate) {
                    $('#save-changes-btn').prop('disabled', false);
                    $('#cancel-changes-btn').prop('disabled', false);
                    console.log('Enabling save/cancel buttons due to status change');
                } else {
                    // Only disable if there are no other changes
                    const hasOtherChanges = $('.editable-field').toArray().some(field => {
                        const $field = $(field);
                        if ($field.prop('disabled')) return false;

                        const name = $field.attr('name');
                        if (!name) return false;

                        const originalValue = originalValues[name];
                        const currentValue = $field.val();

                        if (originalValue === undefined) return false;
                        return originalValue !== currentValue;
                    });

                    if (!hasOtherChanges) {
                        $('#save-changes-btn').prop('disabled', true);
                        $('#cancel-changes-btn').prop('disabled', true);
                        console.log('Disabling save/cancel buttons due to no changes');
                    }
                }
            }, 0);

            // Check if there are any other changes
            const hasEnabledFields = $('.editable-field').toArray().some(field => !$(field).prop('disabled'));

            // Check if any field values have changed
            const hasValueChanges = $('.editable-field').toArray().some(field => {
                const $field = $(field);
                const name = $field.attr('name');

                // Skip if the field doesn't have a name attribute
                if (!name) return false;

                const originalValue = originalValues[name];
                const currentValue = $field.val();

                // Skip if we don't have an original value stored
                if (originalValue === undefined) return false;

                return originalValue !== currentValue;
            });

            // We'll let the checkSaveButtonVisibility function handle this
            // The setTimeout call above will ensure it runs after this function completes

            // CRITICAL FIX: Restore the edit button to its previous state
            // This ensures clicking the status toggle doesn't affect the edit button
            $('#edit-account-btn').prop('disabled', wasEditButtonDisabled);
            console.log('Restored edit button to previous state:', wasEditButtonDisabled ? 'disabled' : 'enabled');
            @else
            alert('{{ __("You do not have permission to change account status") }}');
            @endif
        });

        // Software type change
        $('#software-type').on('change', function() {
            softwareTypeChange();
        });

        // Sync with school checkbox
        $('#sync-with-school').on('change', function() {
            const isChecked = $(this).is(':checked');

            if (isChecked) {
                // Log sync enabled to console
                console.log('Account sync with school enabled');

                // Lock down school-related fields
                lockSchoolRelatedFields();
            } else {
                // Log sync disabled to console
                console.log('Account sync with school disabled');

                // Unlock school-related fields
                unlockSchoolRelatedFields();
            }

            // Enable the save button
            checkSaveButtonVisibility();
        });

        // Province change
        $('#province').on('change', function(e){
            e.preventDefault();
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            let province = $('#province').val();
            let districtDOM = $('#district');
            $.ajax({
                type:'GET',
                url: "/api/v1/provinces/{id}/districts".replace("{id}", province),
                success:function(response){
                    $('option', districtDOM).remove();
                    $.each(response.data, function(){
                        $('<option/>', {
                            'value': this.code,
                            'text': this.name
                        }).appendTo('#district');
                    });
                }
            });
        });
    });

    function softwareTypeChange() {
        let softwareTypes = $('#software-type').val();
        let softwareTypeEnabled = !$('#software-type').prop('disabled');

        // Check if sync with school is enabled
        const syncWithSchoolEnabled = $('#sync-with-school').length > 0 && $('#sync-with-school').is(':checked');

        if (softwareTypes && softwareTypes.includes('{{ \App\Models\Account::SOFTWARE_MAMNON }}')) {
            $('#mamnon-account-type').show();
            // If software type is being edited and sync with school is not enabled, enable the mamnon account type field
            if (softwareTypeEnabled && !syncWithSchoolEnabled) {
                $('[name="mamnon_account_type"]').prop('disabled', false);
            } else if (syncWithSchoolEnabled) {
                // If sync with school is enabled, keep the field disabled
                $('[name="mamnon_account_type"]').prop('disabled', true);
            }
        } else {
            $('#mamnon-account-type').hide();
        }

        if (softwareTypes && softwareTypes.includes('{{ \App\Models\Account::SOFTWARE_TIEUHOC }}')) {
            $('#tieuhoc-account-type').show();
            // If software type is being edited and sync with school is not enabled, enable the tieuhoc account type field
            if (softwareTypeEnabled && !syncWithSchoolEnabled) {
                $('[name="tieuhoc_account_type"]').prop('disabled', false);
            } else if (syncWithSchoolEnabled) {
                // If sync with school is enabled, keep the field disabled
                $('[name="tieuhoc_account_type"]').prop('disabled', true);
            }
        } else {
            $('#tieuhoc-account-type').hide();
        }
    }

    function checkSaveButtonVisibility() {
        // Check if any fields are enabled for editing
        const hasEnabledFields = $('.editable-field').toArray().some(field => !$(field).prop('disabled'));

        // Check if status has changed - ensure we're comparing integers to avoid type issues
        const dataOriginalStatus = parseInt($('#status-toggle').data('original-status'));
        const currentStatus = parseInt($('#status-input').val());

        // This is the critical fix - ensure we're comparing the same types
        const hasStatusChange = currentStatus !== dataOriginalStatus;

        // Check if sync with school is checked
        const syncWithSchool = $('#sync-with-school').length > 0 && $('#sync-with-school').is(':checked');

        // Check if any field values have changed
        const hasValueChanges = $('.editable-field').toArray().some(field => {
            const $field = $(field);
            const name = $field.attr('name');

            // Skip if the field doesn't have a name attribute
            if (!name) return false;

            const originalValue = originalValues[name];
            const currentValue = $field.val();

            // Skip if we don't have an original value stored
            if (originalValue === undefined) return false;

            // For multi-select fields
            if ($field.is('select[multiple]')) {
                if (!originalValue || !currentValue) return false;
                if (originalValue.length !== currentValue.length) return true;
                return !originalValue.every(val => currentValue.includes(val));
            }

            return originalValue !== currentValue;
        });

        // Determine if there are any changes that need to be saved
        const hasChanges = hasEnabledFields || hasStatusChange || hasValueChanges;

        // Log the state for debugging
        console.log('Save button visibility check:', {
            hasEnabledFields,
            hasStatusChange,
            hasValueChanges,
            syncWithSchool,
            dataOriginalStatus: dataOriginalStatus,
            currentStatus: currentStatus,
            hasChanges
        });

        // If we're in edit mode (any field is enabled) or any status has changed
        if (hasChanges) {
            // Only disable edit button if fields are enabled (we're in edit mode)
            // This is the key fix - don't disable the edit button when only toggling status
            if (hasEnabledFields) {
                $('#edit-account-btn').prop('disabled', true);
            }

            $('#save-changes-btn').prop('disabled', false);
            $('#cancel-changes-btn').prop('disabled', false);
            console.log('Enabling save/cancel buttons due to changes');
        } else {
            // If no fields are enabled and no status changes, reset all buttons
            $('#edit-account-btn').prop('disabled', false);
            $('#save-changes-btn').prop('disabled', true);
            $('#cancel-changes-btn').prop('disabled', true);
            console.log('Disabling save/cancel buttons due to no changes');
        }

        // Add visual indicator if there are changes
        if (hasChanges) {
            $('#save-changes-btn').addClass('btn-pulse').css('animation', 'pulse 1.5s infinite');
        } else {
            $('#save-changes-btn').removeClass('btn-pulse').css('animation', '');
        }
    }

    // Function to lock school-related fields when sync with school is enabled
    function lockSchoolRelatedFields() {
        // Disable software type fields
        $('#software-type').prop('disabled', true);

        // Disable account type fields
        $('[name="mamnon_account_type"]').prop('disabled', true);
        $('[name="tieuhoc_account_type"]').prop('disabled', true);

        // Disable time expired field
        $('[name="time_expired"]').prop('disabled', true);

        // Disable province and district fields
        $('#province').prop('disabled', true);
        $('#district').prop('disabled', true);

        // Add visual indicator that these fields are locked by school sync
        $('.school-synced-field').addClass('school-sync-locked').css({
            'background-color': '#f8f9fa',
            'border-color': '#ccc',
            'cursor': 'not-allowed'
        });

        // Add a note to indicate fields are locked
        if ($('.sync-lock-note').length === 0) {
            $('<div class="sync-lock-note" style="color: #856404; background-color: #fff3cd; border: 1px solid #ffeeba; padding: 10px; margin-top: 15px; border-radius: 4px;">')
                .html('<i class="fa fa-lock"></i> {{ __("messages.school_sync_fields_locked") }}')
                .insertAfter('#software-type')
                .clone()
                .insertAfter('[name="time_expired"]')
                .clone()
                .insertAfter('#district');
        }
    }

    // Function to unlock school-related fields when sync with school is disabled
    function unlockSchoolRelatedFields() {
        // Enable software type fields if in edit mode
        if ($('#edit-account-btn').prop('disabled')) {
            $('#software-type').prop('disabled', false);

            // Enable account type fields
            $('[name="mamnon_account_type"]').prop('disabled', false);
            $('[name="tieuhoc_account_type"]').prop('disabled', false);

            // Enable time expired field
            $('[name="time_expired"]').prop('disabled', false);

            // Enable province and district fields
            $('#province').prop('disabled', false);
            $('#district').prop('disabled', false);
        }

        // Remove visual indicators
        $('.school-synced-field').removeClass('school-sync-locked').css({
            'background-color': '',
            'border-color': '',
            'cursor': ''
        });

        // Remove the lock notes
        $('.sync-lock-note').remove();
    }

    // Initialize the lock state based on the initial sync checkbox state
    $(function() {
        if ($('#sync-with-school').length > 0 && $('#sync-with-school').is(':checked')) {
            lockSchoolRelatedFields();
        }
    });

    // Add form submission handler
    $(function() {
        $('#account-form').on('submit', function(e) {
            // Store which fields are currently disabled due to school sync
            const syncWithSchoolEnabled = $('#sync-with-school').length > 0 && $('#sync-with-school').is(':checked');
            const disabledFields = [];

            if (syncWithSchoolEnabled) {
                // Store the values of school-synced fields before enabling them
                // This ensures we submit the correct values even if they were locked
                const schoolSyncedFields = [
                    '#software-type',
                    '[name="mamnon_account_type"]',
                    '[name="tieuhoc_account_type"]',
                    '[name="time_expired"]',
                    '#province',
                    '#district'
                ];

                schoolSyncedFields.forEach(selector => {
                    const $field = $(selector);
                    if ($field.length > 0 && $field.prop('disabled')) {
                        disabledFields.push({
                            selector: selector,
                            value: $field.val()
                        });
                    }
                });
            }

            // Temporarily enable all fields to ensure they're included in the form submission
            $('.editable-field').prop('disabled', false);

            // Enable software type fields based on current account settings
            if ({{ $account->is_mamnon }} === 1) {
                $('input[name="software_types[]"][value="{{ \App\Models\Account::SOFTWARE_MAMNON }}"]').prop('disabled', false);
            }
            if ({{ $account->is_tieuhoc }} === 1) {
                $('input[name="software_types[]"][value="{{ \App\Models\Account::SOFTWARE_TIEUHOC }}"]').prop('disabled', false);
            }

            // Restore the values of school-synced fields if they were disabled
            if (syncWithSchoolEnabled) {
                disabledFields.forEach(field => {
                    $(field.selector).val(field.value);
                });
            }

            // Get the current status value
            const statusValue = $('#status-input').val();

            // Log form data for debugging
            console.log('Form submitted with status:', statusValue);

            // Ensure the status input is included in the form
            if ($('#status-input').length === 0) {
                console.error('Status input field not found!');
                // Create it if it doesn't exist
                $('<input>').attr({
                    type: 'hidden',
                    name: 'status',
                    id: 'status-input',
                    value: $('#status-toggle').data('original-status')
                }).appendTo('#account-form');
                console.log('Created status input with value:', $('#status-input').val());
            } else {
                // Make sure the status value is properly set
                $('#status-input').val(statusValue);
                console.log('Confirmed status input value:', $('#status-input').val());
            }

            // Log form submission to console
            console.log('Submitting form with status: ' + statusValue);

            // Log all form data for debugging
            const formData = {};
            $(this).serializeArray().forEach(item => {
                formData[item.name] = item.value;
            });
            console.log('All form data being submitted:', formData);

            // Allow form submission to continue
            return true;
        });
    });


</script>
@endsection

