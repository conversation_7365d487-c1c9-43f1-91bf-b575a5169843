@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.add.account.game'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('account') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.account.game') }}
        </a>
    </li>

@endsection

@section('content')

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('account.store') }}" method="post">
                        {{ csrf_field() }}
                        <input type="hidden" name="active" value="1">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.name') }}</label>
                                    <input type="text" name="name" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.name') }}" required="" value="{{ old('name') }}" autofocus>
                                    @if($errors->has('name'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('name') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('name_org') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.name_org') }}</label>
                                    <input type="text" name="name_org" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.name_org') }}" required="" value="{{ old('name_org') }}" autofocus>
                                    @if($errors->has('name_org'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('name_org') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('phone_number') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.phone_number') }}</label>
                                    <input type="text" name="phone_number" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.phone_number') }}" required="" value="{{ old('phone_number') }}" autofocus>
                                    @if($errors->has('phone_number'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('phone_number') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('class') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.class') }}</label>
                                    <input type="text" name="class" class="form-control" maxlength="30" placeholder="{{ __('messages.class') }}" required="" value="{{ old('class') }}" autofocus>
                                    @if($errors->has('class'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('class') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('email') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.email') }}</label>
                                    <input type="email" name="email" class="form-control" placeholder="{{ __('messages.email') }}" required="" value="{{ old('email') }}">
                                    @if($errors->has('email'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('email') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('province') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.province') }}</label>
                                    <select name="province" id="province" class="form-control select2" data-placeholder="{{ __('messages.province') }}" required="">
                                        @foreach($provinces as $key => $province)
                                            <option value="{{ $key }}"> {{ $province['name'] }} </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('province'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('province') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('district') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.district') }}</label>
                                    <select name="district" id="district" class="form-control select2" data-placeholder="{{ __('messages.district') }}" required="">
                                        @foreach($firstDistricts as $key => $firstDistrict)
                                            <option value="{{ $key }}"> {{ $firstDistrict['name'] }} </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('district'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('district') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('roles') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.status') }}</label>
                                    <select name="status" class="form-control select2" data-placeholder="Trạng thái" required="">
                                        <option value="{{ \App\Models\Account::STATUS_ACTIVE }}"> {{ __('messages.status.active') }} </option>
                                        <option value="{{ \App\Models\Account::STATUS_LOCK}}"> {{ __('messages.status.locked') }} </option>
                                    </select>
                                    @if($errors->has('roles'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('roles') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('software_types') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.software_type') }}</label>
                                    <select name="software_types[]" class="form-control select2" id="software-type" multiple="multiple" data-placeholder="{{ __('messages.software_type') }}" required="">
                                        <option value="{{ \App\Models\Account::SOFTWARE_MAMNON }}" selected> {{ __('messages.software_mamnon') }} </option>
                                        <option value="{{ \App\Models\Account::SOFTWARE_TIEUHOC }}" selected> {{ __('messages.software_tieuhoc') }} </option>
                                    </select>
                                    @if($errors->has('software_types'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('software_types') }}</strong>
                                        </span>
                                    @endif
                                </div>

                                <div class="form-group col-lg-6  {{ $errors->has('mamnon_account_type') ? 'has-error' : '' }}" id="mamnon-account-type">
                                    <label for="nome">{{ __('messages.mamnon_account_type') }}</label>
                                    <select name="mamnon_account_type" class="form-control select2" data-placeholder="{{ __('messages.mamnon_account_type') }}" >
                                        @foreach($mamnonAccountTypes as $mamnonAccountType)
                                            <option value="{{ $mamnonAccountType->id }}" selected> {{ $mamnonAccountType->name }} </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('mamnon_account_type'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('mamnon_account_type') }}</strong>
                                        </span>
                                    @endif
                                </div>

                                <div class="form-group col-lg-6 {{ $errors->has('tieuhoc_account_type') ? 'has-error' : '' }}" id="tieuhoc-account-type">
                                    <label for="nome">{{ __('messages.tieuhoc_account_type') }}</label>
                                    <select name="tieuhoc_account_type" class="form-control select2" data-placeholder="{{ __('messages.tieuhoc_account_type') }}" >
                                        @foreach($tieuhocAccountTypes as $tieuhocAccountType)
                                            <option value="{{ $tieuhocAccountType->id }}" selected> {{ $tieuhocAccountType->name }} </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('tieuhoc_account_type'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('tieuhoc_account_type') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6"></div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.add') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

<script>
    $(function(){
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return "Nenhum registro encontrado.";
                }
            }
        });
    });

    $('#software-type').on('change', function(e){
        let sofwareTypes = $('#software-type').val();
        if(sofwareTypes.includes('{{ \App\Models\Account::SOFTWARE_MAMNON }}')){
            $('#mamnon-account-type').show();
        } else {
            $('#mamnon-account-type').hide();
        }

        if(sofwareTypes.includes('{{ \App\Models\Account::SOFTWARE_TIEUHOC }}')){
            $('#tieuhoc-account-type').show();
        } else {
            $('#tieuhoc-account-type').hide();
        }
    });

    $('#province').on('change', function(e){
        e.preventDefault();
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        let province = $('#province').val();
        let districtDOM = $('#district');
        $.ajax({
            type:'GET',
            url: "/api/v1/provinces/{id}/districts".replace("{id}", province),
            success:function(response){
                $('option', districtDOM).remove();
                $.each(response.data, function(){
                    $('<option/>', {
                        'value': this.code,
                        'text': this.name
                    }).appendTo('#district');
                });
            }

        });
    });

</script>

@endsection
