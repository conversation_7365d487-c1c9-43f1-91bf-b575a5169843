@extends('layouts.AdminLTE.index')

@section('icon_page', 'unlock-alt')

@section('title', __('messages.index.account_type'))

@section('menu_pagina')
	<li role="presentation">
		<a href="{{ route('account_type.create') }}" class="link_menu_page">
			<i class="fa fa-plus"></i> {{ __('messages.add.account_type') }}
		</a>
	</li>
@endsection

@section('content')
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title"><i class="fa fa-unlock-alt"></i> {{ __('messages.index.account_type') }}</h3>
            <div class="box-tools">
                <a href="{{ route('account_type.create') }}" class="btn btn-primary btn-sm">
                    <i class="fa fa-plus"></i> {{ __('messages.add.account_type') }}
                </a>
            </div>
        </div>

        <div class="box-body">
            <!-- Search and Filter Bar -->
            <div class="row search-filter-bar">
                <div class="col-md-12">
                    <form id="search-form" method="GET" action="{{ route('account_type') }}" class="form-inline">
                        <div class="form-group search-box">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="{{ __('messages.search') }}..." value="{{ request('search') }}">
                                <span class="input-group-btn">
                                    <button type="submit" class="btn btn-primary" title="{{ __('messages.search') }}">
                                        <i class="fa fa-search"></i>
                                    </button>
                                    @if(request('search') || request('filter') || request('sort') || request('direction') || request('per_page'))
                                        <a href="{{ route('account_type') }}" class="btn btn-default" title="{{ __('messages.reset_filters') }}">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="form-group filter-box">
                            <select name="filter" class="form-control" onchange="this.form.submit()">
                                <option value="">{{ __('messages.filter_by') }}</option>
                                <option value="has_materials" {{ request('filter') == 'has_materials' ? 'selected' : '' }}>{{ __('messages.has_materials_filter') }}</option>
                                <option value="no_materials" {{ request('filter') == 'no_materials' ? 'selected' : '' }}>{{ __('messages.no_materials_filter') }}</option>
                                <option value="has_courses" {{ request('filter') == 'has_courses' ? 'selected' : '' }}>{{ __('messages.has_courses_filter') }}</option>
                                <option value="no_courses" {{ request('filter') == 'no_courses' ? 'selected' : '' }}>{{ __('messages.no_courses_filter') }}</option>
                            </select>
                        </div>

                        <div class="form-group per-page-box">
                            <select name="per_page" class="form-control" onchange="this.form.submit()">
                                <option value="15" {{ request('per_page') == 15 || !request('per_page') ? 'selected' : '' }}>15 {{ __('messages.per_page') }}</option>
                                <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 {{ __('messages.per_page') }}</option>
                                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 {{ __('messages.per_page') }}</option>
                                <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 {{ __('messages.per_page') }}</option>
                            </select>
                        </div>

                        <input type="hidden" name="sort" value="{{ request('sort', 'created_at') }}">
                        <input type="hidden" name="direction" value="{{ request('direction', 'asc') }}">
                    </form>
                </div>
            </div>

            <!-- Results Count -->
            <div class="row">
                <div class="col-md-12">
                    <div class="results-info">
                        {{ __('messages.pagination_info', ['from' => $accountTypes->firstItem() ?? 0, 'to' => $accountTypes->lastItem() ?? 0, 'total' => $accountTypes->total()]) }}
                        @if(request('search'))
                            <span class="search-term">
                                {{ __('messages.for') }} "{{ request('search') }}"
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Table -->
            <div class="row">
                <div class="col-md-12">
                    <div class="table-responsive">
                        <table id="tabelapadrao" class="table table-condensed table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th width="15%">
                                        <a href="{{ route('account_type', array_merge(request()->except(['sort', 'direction']), ['sort' => 'name', 'direction' => (request('sort') == 'name' && request('direction') == 'asc') ? 'desc' : 'asc'])) }}" class="sort-link">
                                            {{ __('messages.name') }}
                                            @if(request('sort') == 'name')
                                                <i class="fa fa-sort-{{ request('direction') == 'asc' ? 'up' : 'down' }}"></i>
                                            @else
                                                <i class="fa fa-sort"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th width="8%">
                                        <a href="{{ route('account_type', array_merge(request()->except(['sort', 'direction']), ['sort' => 'code', 'direction' => (request('sort') == 'code' && request('direction') == 'asc') ? 'desc' : 'asc'])) }}" class="sort-link">
                                            {{ __('messages.code') }}
                                            @if(request('sort') == 'code')
                                                <i class="fa fa-sort-{{ request('direction') == 'asc' ? 'up' : 'down' }}"></i>
                                            @else
                                                <i class="fa fa-sort"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th width="40%">
                                        <a href="{{ route('account_type', array_merge(request()->except(['sort', 'direction']), ['sort' => 'description', 'direction' => (request('sort') == 'description' && request('direction') == 'asc') ? 'desc' : 'asc'])) }}" class="sort-link">
                                            {{ __('messages.description') }}
                                            @if(request('sort') == 'description')
                                                <i class="fa fa-sort-{{ request('direction') == 'asc' ? 'up' : 'down' }}"></i>
                                            @else
                                                <i class="fa fa-sort"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th width="10%">
                                        <a href="{{ route('account_type', array_merge(request()->except(['sort', 'direction']), ['sort' => 'created_at', 'direction' => (request('sort') == 'created_at' && request('direction') == 'asc') ? 'desc' : 'asc'])) }}" class="sort-link default-sort">
                                            {{ __('messages.created_at') }}
                                            @if(request('sort') == 'created_at' || request('sort') == null)
                                                <i class="fa fa-sort-{{ request('direction', 'asc') == 'asc' ? 'up' : 'down' }}"></i>
                                            @else
                                                <i class="fa fa-sort"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th width="10%">{{ __('messages.material_count') }}</th>
                                    <th width="10%">{{ __('messages.course_count') }}</th>
                                    <th width="5%" class="text-center">{{ __('messages.action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
								@foreach($accountTypes as $accountType)
                                    <tr>
                                        <td>{{ $accountType->name }}</td>
                                        <td>{{ $accountType->code }}</td>
                                        <td>{{ $accountType->description }}</td>
                                        <td>{{ $accountType->created_at->format('d/m/Y H:i') }}</td>
                                        <td>
                                            @php
                                                $materialCount = count($accountType->materialList);
                                            @endphp
                                            <div class="badge-wrapper">
                                                @if($accountType->nonExistentCount > 0)
                                                    <span class="notification-indicator" title="{{ __('messages.non_existent_materials') }}">
                                                        {{ $accountType->nonExistentCount }}
                                                    </span>
                                                @endif
                                                <span class="badge bg-blue">{{ $materialCount }}</span>
                                            </div>
                                            @if($materialCount > 0)
                                                <button type="button" class="btn btn-xs btn-default view-btn" data-toggle="modal" data-target="#modal-materials-{{ $accountType->id }}" title="{{ __('messages.view_all') }}">
                                                    <i class="fa fa-list"></i> <span class="view-text">{{ __('messages.view') }}</span>
                                                </button>
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $courseCount = $accountType->courses ? $accountType->courses->count() : 0;
                                            @endphp
                                            <div class="badge-wrapper">
                                                <span class="badge bg-green">{{ $courseCount }}</span>
                                            </div>
                                            @if($courseCount > 0)
                                                <button type="button" class="btn btn-xs btn-default view-btn" data-toggle="modal" data-target="#modal-courses-{{ $accountType->id }}" title="{{ __('messages.view_all') }}">
                                                    <i class="fa fa-list"></i> <span class="view-text">{{ __('messages.view') }}</span>
                                                </button>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <a class="btn btn-warning btn-xs" href="{{ route('account_type.edit', $accountType->id) }}" title="{{ __('messages.edit') }} {{ $accountType->name }}"><i class="fa fa-pencil"></i></a>
                                            <a class="btn btn-danger btn-xs" href="#" title="{{ __('messages.delete') }} {{ $accountType->name}}" data-toggle="modal" data-target="#modal-delete-{{ $accountType->id }}"><i class="fa fa-trash"></i></a>
                                        </td>
                                    </tr>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="modal-delete-{{ $accountType->id }}">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">×</span>
                                                    </button>
                                                    <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                                </div>
                                                <div class="modal-body">
                                                    <p>{{ __('messages.do_you_want_delete') }} ({{ $accountType->name }}) ?</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                                    <a href="{{ route('account_type.destroy', $accountType->id) }}" ><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Materials Modal -->
                                    <div class="modal fade" id="modal-materials-{{ $accountType->id }}">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">×</span>
                                                    </button>
                                                    <h4 class="modal-title">{{ __('messages.material_list') }} - {{ $accountType->name }}</h4>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="compact-tags-container">
                                                        <!-- Debug info -->
                                                        <div style="display:none;">
                                                            Existing Materials: {{ json_encode($existingMaterials) }}
                                                        </div>

                                                        @if(isset($accountType->paginatedMaterials))
                                                            @foreach($accountType->paginatedMaterials as $material)
                                                                @php
                                                                    // Try to match by material ID (bin-01, bin-02, etc.)
                                                                    $materialId = null;
                                                                    $exists = false;

                                                                    // Check if the material exists in our database
                                                                    if (array_key_exists($material, $existingMaterials)) {
                                                                        $exists = true;
                                                                        $materialId = $existingMaterials[$material];
                                                                    }

                                                                    // If not found and it looks like "bin-XX", try to extract the number
                                                                    if (!$exists && preg_match('/bin-(\d+)/', $material, $matches)) {
                                                                        $materialNumber = $matches[1];
                                                                        // Remove leading zeros
                                                                        $materialNumber = ltrim($materialNumber, '0');

                                                                        // Try with just the number
                                                                        if (array_key_exists($materialNumber, $existingMaterials)) {
                                                                            $exists = true;
                                                                            $materialId = $existingMaterials[$materialNumber];
                                                                        }

                                                                        // Try with "Bài học XX" format
                                                                        $baiHocFormat = 'Bài học ' . $materialNumber;
                                                                        if (!$exists && array_key_exists($baiHocFormat, $existingMaterials)) {
                                                                            $exists = true;
                                                                            $materialId = $existingMaterials[$baiHocFormat];
                                                                        }

                                                                        // Try with "1.XX" format
                                                                        $dotFormat = '1.' . $materialNumber;
                                                                        if (!$exists && array_key_exists($dotFormat, $existingMaterials)) {
                                                                            $exists = true;
                                                                            $materialId = $existingMaterials[$dotFormat];
                                                                        }
                                                                    }

                                                                    $cssClass = $exists ? 'exists' : 'not-exists';
                                                                @endphp
                                                                <a href="{{ $exists ? route('material.edit', $materialId) : route('material.create') . '?content=' . urlencode($material) }}"
                                                                   class="tag material-tag {{ $cssClass }}"
                                                                   data-material="{{ $material }}"
                                                                   @if($exists) data-material-id="{{ $materialId }}" @endif
                                                                   title="{{ $exists ? __('messages.edit_material') : __('messages.create_material') }}">
                                                                    {{ $material }}
                                                                </a>
                                                            @endforeach

                                                            <div class="pagination-container" id="pagination-container-{{ $accountType->id }}">
                                                                <div class="pagination-wrapper">
                                                                        @php
                                                                            $paginator = $accountType->paginatedMaterials;
                                                                            $totalPages = $paginator->lastPage();
                                                                            $currentPage = $paginator->currentPage();
                                                                        @endphp

                                                                        <ul class="pagination">
                                                                            {{-- Previous Page Link --}}
                                                                            @if ($currentPage > 1)
                                                                                <li>
                                                                                    <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $currentPage - 1 }}" title="{{ __('messages.previous_page') }}">&laquo;</a>
                                                                                </li>
                                                                            @else
                                                                                <li class="disabled">
                                                                                    <span>&laquo;</span>
                                                                                </li>
                                                                            @endif

                                                                            {{-- Page Number Links --}}
                                                                            @for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++)
                                                                                <li class="{{ $i == $currentPage ? 'active' : '' }}">
                                                                                    <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $i }}" title="{{ __('messages.page') }} {{ $i }}">{{ $i }}</a>
                                                                                </li>
                                                                            @endfor

                                                                            {{-- Next Page Link --}}
                                                                            @if ($currentPage < $totalPages)
                                                                                <li>
                                                                                    <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $currentPage + 1 }}" title="{{ __('messages.next_page') }}">&raquo;</a>
                                                                                </li>
                                                                            @else
                                                                                <li class="disabled">
                                                                                    <span>&raquo;</span>
                                                                                </li>
                                                                            @endif
                                                                        </ul>

                                                                </div>
                                                                <div class="pagination-info">
                                                                    {{ __('messages.pagination_info', ['from' => $paginator->firstItem(), 'to' => $paginator->lastItem(), 'total' => $paginator->total()]) }}
                                                                </div>
                                                            </div>
                                                        @else
                                                            <p>{{ __('messages.no_results_found') }}</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Courses Modal -->
                                    <div class="modal fade" id="modal-courses-{{ $accountType->id }}">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">×</span>
                                                    </button>
                                                    <h4 class="modal-title">{{ __('messages.courses') }} - {{ $accountType->name }}</h4>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="compact-tags-container">
                                                        @if($accountType->courses != null)
                                                            @foreach($accountType->courses as $course)
                                                                <span class="tag course-tag" data-course-id="{{ $course->id }}">{{ $course->title_en }}</span>
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ __('messages.action.close') }}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
								@endforeach
							</tbody>
							<tfoot>
								<tr>
									<th>{{ __('messages.name') }}</th>
									<th>{{ __('messages.code') }}</th>
									<th>{{ __('messages.description') }}</th>
									<th>{{ __('messages.created_at') }}</th>
									<th>{{ __('messages.material_count') }}</th>
									<th>{{ __('messages.course_count') }}</th>
									<th class="text-center">{{ __('messages.action') }}</th>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
				<div class="col-md-12 text-center">
					{{ $accountTypes->appends(request()->except('page'))->links() }}
				</div>
			</div>
		</div>
	</div>
@endsection

@section('layout_css')
<style>
    .compact-tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }

    .compact-tags-container .tag {
        background-color: #3c8dbc;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        margin-bottom: 5px;
        display: inline-block;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .compact-tags-container .tag:hover {
        opacity: 0.9;
        transform: translateY(-1px);
        box-shadow: 0 2px 3px rgba(0,0,0,0.1);
        text-decoration: none;
        color: white;
    }

    .compact-tags-container .tag.exists {
        background-color: #00a65a; /* Green */
    }

    .compact-tags-container .tag.not-exists {
        background-color: #dd4b39; /* Red */
    }

    .badge {
        font-size: 14px;
        padding: 5px 8px;
        display: inline-block;
        min-width: 30px;
    }

    .badge-wrapper {
        position: relative;
        display: inline-block;
    }

    .notification-indicator {
        position: absolute;
        left: -14px;
        top: -8px;
        font-size: 11px;
        padding: 2px 4px;
        border-radius: 4px;
        min-width: 18px;
        height: 18px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #dd4b39;
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        animation: pulse 2s infinite;
        z-index: 1;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(221, 75, 57, 0.7);
        }
        70% {
            box-shadow: 0 0 0 5px rgba(221, 75, 57, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(221, 75, 57, 0);
        }
    }

    .pagination-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 15px;
        width: 100%;
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        margin-bottom: 10px;
        position: relative;
    }

    .pagination-container .pagination {
        margin: 0;
    }

    .pagination-info {
        font-size: 12px;
        color: #777;
        text-align: center;
    }

    .non-existent-info {
        color: #dd4b39;
        font-weight: bold;
        display: block;
        margin-top: 5px;
    }

    /* Make modal body scrollable if needed */
    .modal-body {
        max-height: 70vh;
        overflow-y: auto;
        position: relative;
        padding: 8px;
    }

    /* Table cell styling */
    #tabelapadrao > thead > tr > th,
    #tabelapadrao > tbody > tr > td {
        padding: 12px 8px; /* Increased padding to make rows taller */
        vertical-align: middle;
        border: 1px solid #ddd;
    }

    /* Ensure all table headers have consistent styling */
    #tabelapadrao > thead > tr > th,
    #tabelapadrao > tfoot > tr > th {
        font-weight: bold;
        color: #000;
    }

    /* Improve table appearance */
    #tabelapadrao {
        border-collapse: collapse;
        border-spacing: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #ddd;
    }

    /* Name and code columns */
    #tabelapadrao > tbody > tr > td:nth-child(1),
    #tabelapadrao > tbody > tr > td:nth-child(2) {
        font-weight: 500;
        font-size: 14px;
        white-space: nowrap;
    }

    /* Description column - allow wrapping */
    #tabelapadrao > tbody > tr > td:nth-child(3) {
        white-space: normal;
        font-size: 14px;
        line-height: 1.5;
        max-width: 400px; /* Ensure description has enough space */
    }

    /* Date column */
    #tabelapadrao > tbody > tr > td:nth-child(4) {
        white-space: nowrap;
        font-size: 13px;
    }

    /* Material and course count columns */
    #tabelapadrao > tbody > tr > td:nth-child(5),
    #tabelapadrao > tbody > tr > td:nth-child(6) {
        white-space: nowrap;
        text-align: center;
    }

    /* Make the view buttons more compact */
    .view-btn {
        margin-left: 2px;
        padding: 2px 4px;
        font-size: 11px;
    }

    /* Make the view text more compact */
    .view-text {
        font-size: 10px;
    }

    /* Alternate row colors for better readability */
    #tabelapadrao tbody tr:nth-child(odd) {
        background-color: #f9f9f9;
    }

    #tabelapadrao tbody tr:hover {
        background-color: #e8f4fc;
    }

    /* Search and Filter Bar */
    .search-filter-bar {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .search-box {
        margin-right: 10px;
    }

    .search-box .input-group {
        width: 300px;
    }

    .filter-box {
        margin-right: 10px;
    }

    .filter-box select {
        min-width: 150px;
    }

    .per-page-box {
        margin-right: 10px;
    }

    .results-info {
        margin-bottom: 15px;
        color: #777;
        font-size: 13px;
    }

    .search-term {
        font-weight: bold;
        color: #3c8dbc;
    }

    .sort-link {
        color: #000;
        font-weight: bold;
        text-decoration: none;
        white-space: nowrap;
    }

    .sort-link:hover {
        color: #3c8dbc;
        text-decoration: none;
    }

    .sort-link i {
        margin-left: 5px;
        color: #3c8dbc;
    }

    .sort-link.default-sort {
        font-weight: bold;
        color: #000;
    }

    .sort-link.default-sort i {
        color: #3c8dbc;
    }

    /* Loading indicator */
    .ajax-loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10;
        font-weight: bold;
    }

    /* Make the close X button bigger and red */
    .modal-header .close {
        font-size: 28px;
        color: #dd4b39;
        opacity: 1;
        font-weight: bold;
        margin-top: -5px;
    }

    .modal-header .close:hover {
        color: #c9302c;
    }
</style>
@endsection

@section('layout_js')
<script>
    $(document).ready(function() {
        // Handle course tags - these already exist in the database
        $('.course-tag').addClass('exists');

        // Material tags now use direct links, no click handler needed

        // Click handler for course tags
        $(document).on('click', '.course-tag', function() {
            const courseId = $(this).data('course-id');
            window.location.href = '{{ url("course/edit") }}/' + courseId;
        });

        // Handle material pagination with AJAX
        $(document).on('click', '.material-page-link', function(e) {
            e.preventDefault();
            const accountTypeId = $(this).data('account-type-id');
            const page = $(this).data('page');
            const modalBody = $(this).closest('.modal-body');

            // Show loading indicator
            modalBody.append('<div class="ajax-loading">{{ __('messages.loading') }}</div>');

            $.ajax({
                url: '{{ route("account-type.materials") }}',
                type: 'GET',
                data: {
                    account_type_id: accountTypeId,
                    page: page
                },
                success: function(response) {
                    // Replace the content of the materials container
                    modalBody.find('.compact-tags-container').html(response);

                    // Remove loading indicator
                    modalBody.find('.ajax-loading').remove();
                },
                error: function() {
                    alert('Error loading materials. Please try again.');
                    modalBody.find('.ajax-loading').remove();
                }
            });
        });
    });
</script>
@endsection
