@extends('layouts.AdminLTE.index')

@section('icon_page', 'unlock-alt')

@section('title', __('messages.index.account_backup'))

@section('css')
<style>
    /* Ensure table columns have appropriate widths */
    #tabelapadrao th, #tabelapadrao td {
        padding: 12px 8px; /* Increased padding for taller rows */
        vertical-align: middle;
        border: 1px solid #ddd;
    }

    /* Improve table appearance */
    #tabelapadrao {
        border-collapse: collapse;
        border-spacing: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #ddd;
    }

    /* Alternate row colors for better readability */
    #tabelapadrao tbody tr:nth-child(odd) {
        background-color: #f9f9f9;
    }

    #tabelapadrao tbody tr:hover {
        background-color: #e8f4fc;
    }

    /* Ensure buttons are properly sized and spaced */
    .btn-xs {
        padding: 5px 10px;
        font-size: 12px;
        line-height: 1.5;
        border-radius: 3px;
        margin: 0 3px;
    }
</style>
@endsection

@section('content')

    <div class="box box-primary">
		<div class="box-body">
			<div class="row">
				<div class="col-md-12">
					<div class="table-responsive">
						<table id="tabelapadrao" class="table table-condensed table-bordered table-hover">
							<thead>
								<tr>
									<th width="60%">{{ __('messages.file_name') }}</th>
									<th width="30%">{{ __('messages.created_at') }}</th>
                                    <th width="10%" class="text-center">{{ __('messages.action') }}</th>
								</tr>
							</thead>
							<tbody>
								@foreach($files as $file)
                                    <tr>
                                        <td>{{ $file->file_name }}</td>
                                        <td>{{ $file->created_at->format('d/m/Y H:i') }}</td>
                                        <td class="text-center">
                                            <a class="btn btn-warning  btn-xs" href="{{ route('account_backup.download', $file->id) }}" title="Edit {{ $file->file_name }}"><i class="fa fa-download"></i></a>
                                        </td>
                                    </tr>
								@endforeach
							</tbody>
							<tfoot>
								<tr>
									<th width="60%">{{ __('messages.file_name') }}</th>
									<th width="30%">{{ __('messages.created_at') }}</th>
									<th width="10%" class="text-center">{{ __('messages.action') }}</th>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
				<div class="col-md-12 text-center">
					{{ $files->links() }}
				</div>
			</div>
		</div>
	</div>

@endsection
