@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.add.lesson'))

@section('menu_pagina')

    <li role="presentation">
        <a href="{{ route('lesson') }}" class="link_menu_page">
            <i class="fa fa-user"></i> {{ __('messages.index.lesson') }}
        </a>
    </li>

@endsection

@section('content')

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('lesson.store') }}" method="post">
                        {{ csrf_field() }}
                        <input type="hidden" name="active" value="1">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('title') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.title') }}</label>
                                    <input type="text" name="title" class="form-control" placeholder="{{ __('messages.title') }}" required="" value="{{ old('title', $title ?? '') }}" autofocus>
                                    @if($errors->has('title'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('title') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('course') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.course') }}</label>
                                    <select name="course" id="course" class="form-control select2" data-placeholder="{{ __('messages.course') }}" required="">
                                        @foreach($courses as $course)
                                            <option value="{{ $course->id }}"> {{ $course->title_en }} </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('course'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('course') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6"></div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.add') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('layout_js')

<script>
    $(function(){
        $('.select2').select2({
            "language": {
                "noResults": function(){
                    return "Nenhum registro encontrado.";
                }
            }
        });
    });

</script>

@endsection
