@extends('layouts.AdminLTE.index')

@section('icon_page', 'user')

@section('title', __('messages.index.account.game'))

@section('css')
<style>
    /* Ensure table columns have appropriate widths */
    #tabelapadrao th, #tabelapadrao td {
        padding: 12px 8px; /* Increased padding for taller rows */
        vertical-align: middle;
        border: 1px solid #ddd;
    }

    /* Dashboard tab styling */
    .nav-tabs {
        border-bottom: 2px solid #3c8dbc;
    }

    .nav-tabs > li > a {
        border-radius: 0;
        color: #555;
        margin-right: 2px;
    }

    .nav-tabs > li.active > a,
    .nav-tabs > li.active > a:focus,
    .nav-tabs > li.active > a:hover {
        border: 1px solid #3c8dbc;
        border-bottom-color: transparent;
        background-color: #f9f9f9;
        color: #3c8dbc;
        font-weight: bold;
    }

    .nav-tabs > li > a:hover {
        background-color: #f5f5f5;
        border-color: #eee #eee #3c8dbc;
    }

    .tab-content {
        padding: 15px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-top: none;
    }

    /* Progress bar styling */
    .progress {
        height: 20px;
        margin-bottom: 0;
    }

    .progress-bar {
        line-height: 20px;
        font-weight: bold;
        text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
    }

    /* Label spacing */
    .label {
        margin-right: 5px;
        display: inline-block;
        margin-bottom: 5px;
    }

    /* Allow line breaks in specific columns */
    #tabelapadrao td.text-center,
    #tabelapadrao td:nth-child(4), /* Software type column in school view */
    #tabelapadrao td:nth-child(5) /* Account type column in school view */ {
        white-space: normal;
        line-height: 1.5; /* Increased line height for better readability */
    }

    /* Add some spacing between lines in cells with multiple entries */
    #tabelapadrao td br {
        margin-bottom: 5px;
        content: "";
        display: block;
        margin-top: 5px;
    }

    /* Improve table appearance */
    #tabelapadrao {
        border-collapse: collapse; /* Changed to collapse for better border rendering */
        border-spacing: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #ddd; /* Add outer border */
    }

    #tabelapadrao th {
        background-color: #3c8dbc;
        color: white;
        font-weight: bold;
        padding: 14px 10px; /* Larger padding for headers */
        border-bottom: 2px solid #ddd;
    }

    /* Alternate row colors for better readability */
    #tabelapadrao tbody tr:nth-child(odd) {
        background-color: #f9f9f9;
    }

    #tabelapadrao tbody tr:hover {
        background-color: #e8f4fc;
    }

    /* Add visible borders between rows */
    #tabelapadrao tbody tr {
        border-bottom: 1px solid #ddd;
    }

    /* Make the table cells more readable with visible borders */
    #tabelapadrao td, #tabelapadrao th {
        vertical-align: middle;
        border-top: 1px solid #ddd;
        border-right: 1px solid #ddd; /* Add vertical borders between columns */
        border-bottom: 1px solid #ddd; /* Ensure bottom borders are visible */
    }

    /* Remove border from last column */
    #tabelapadrao td:last-child, #tabelapadrao th:last-child {
        border-right: none;
    }

    /* Make sure the table is responsive */
    .table-responsive {
        overflow-x: auto;
    }

    /* Ensure buttons are properly sized and spaced */
    .btn-xs {
        padding: 5px 10px;
        font-size: 12px;
        line-height: 1.5;
        border-radius: 3px;
        margin: 0 3px;
        transition: all 0.2s ease;
    }

    /* Improve button appearance */
    .btn-default.btn-xs {
        background-color: #f8f9fa;
        border-color: #ddd;
    }

    .btn-default.btn-xs:hover {
        background-color: #e9ecef;
        border-color: #ccc;
    }

    .btn-danger.btn-xs {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger.btn-xs:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    /* Toggle icon rotation */
    .toggle-icon.rotated {
        transform: rotate(90deg);
        transition: transform 0.2s;
    }

    .toggle-icon {
        transition: transform 0.2s;
        display: inline-block;
        width: 14px;
        text-align: center;
    }

    .fa-caret-down {
        transform: rotate(90deg);
    }

    .province-row {
        cursor: pointer;
    }

    .province-row:hover {
        background-color: #f9f9f9;
    }

    /* Collapsible district rows */
    .district-row {
        display: none;
        background-color: #f5f5f5;
    }

    .district-row td {
        padding-left: 30px !important;
    }

    .province-row {
        cursor: pointer;
    }

    .province-row:hover {
        background-color: #e8f4fc !important;
    }

    .province-row.active {
        background-color: #e8f4fc !important;
    }

    .toggle-icon {
        margin-right: 5px;
        transition: transform 0.3s;
    }

    .toggle-icon.rotated {
        transform: rotate(90deg);
    }
</style>
@endsection

@section('menu_pagina')

<li role="presentation">
    @if(!isset($viewMode) || $viewMode == 'account')
    <a href="{{ route('account.create') }}" class="link_menu_page">
        <i class="fa fa-plus"></i> {{ __('messages.add.account.game') }}
    </a>
    @else
    <a href="{{ route('school.create') }}" class="link_menu_page">
        <i class="fa fa-plus"></i> {{ __('messages.add.school') ?: 'Add School' }}
    </a>
    @endif
</li>

@endsection

@section('content')

<div class="box box-primary">
    <div class="box-body">
        <div class="row">
            <form action="{{ route('account') }}" method="get" id="search-form">
                <div class="col-lg-4">
                    <div class="form-group">
                        <label for="nome">{{ __('messages.province') }}</label>
                        <select name="province" id="province" class="form-control select2" data-placeholder="{{ __('messages.province') }}" required="">
                            <option value="-1"> Tất cả </option>
                            @foreach($provinces as $key => $province)
                                <option value="{{ $key }}" @if( $searchProvince == $key) selected @endif> {{ $province['name'] }} </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="form-group">
                        <label for="nome">{{ __('messages.district') }}</label>
                        <select name="district" id="district" class="form-control select2" data-placeholder="{{ __('messages.district') }}" required="">
                            <option value="-1"> Tất cả </option>
                            @foreach($selectedDistricts as $key => $selectedDistrict)
                                <option value="{{ $key }}" @if($searchDistrict == $key) selected @endif> {{ $selectedDistrict['name'] }} </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                        <label for="nome">{{ __('messages.search') }}</label>
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="{{ __('messages.search') }}" value="{{ $keyword }}">
                            <span class="input-group-btn">
                                <button type="submit" class="btn btn-google"><i class="fa fa-fw fa-search"></i> {{ __('messages.search') }}</button>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- View mode toggle -->
                <input type="hidden" name="view_mode" id="view_mode" value="{{ isset($viewMode) ? $viewMode : 'account' }}">


            </form>
            <!-- modal -->
            <div class="modal fade" id="modal-import">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.download_template') }}!!</h4>
                        </div>
                        <div class="modal-footer">
                            <form class="pull-left" action="{{ route('account.export_template') }}" method="get" >
                                <button type="submit" class="btn btn-file btn-xs"><i class="fa fa-fw fa-download"></i> {{ __('messages.action.export_template') }}</button>
                            </form>

                            <form class="pull-right" style="display: flex; flex-direction: column;" action="{{ route('account.import') }}" method="post" enctype="multipart/form-data">
                                {{ csrf_field() }}
                                <input type="file" value="" id="file-import" name="file" style="margin-bottom: 5px; max-width: 200px;">
                                <button type="submit" class="btn btn-file btn-xs"><i class="fa fa-fw fa-upload"></i> {{ __('messages.action.import') }}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="row" style="margin-top: 20px;">
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="view_toggle">{{ __('messages.view_mode') }}</label>
                            <div class="btn-group btn-toggle btn-block">
                                <button type="button" class="btn {{ isset($viewMode) && $viewMode == 'account' ? 'btn-primary active' : 'btn-default' }}" data-mode="account" style="width: 50%;">{{ __('messages.accounts') }}</button>
                                <button type="button" class="btn {{ isset($viewMode) && $viewMode == 'school' ? 'btn-primary active' : 'btn-default' }}" data-mode="school" style="width: 50%;">{{ __('messages.schools') }}</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label>{{ __('messages.import_export') }}</label>
                            <div class="btn-group btn-block">
                                <form action="{{ route('account.export') }}" method="get" style="display: inline-block; width: 50%;">
                                    <button type="submit" class="btn btn-default btn-block"><i class="fa fa-fw fa-download"></i> {{ __('messages.action.export') }}</button>
                                </form>
                                <a data-toggle="modal" data-target="#modal-import" class="btn btn-default" style="width: 50%;">
                                    <i class="fa fa-fw fa-upload"></i> {{ __('messages.action.import') }}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        @if(!isset($viewMode) || $viewMode == 'account')
                        <div class="form-group">
                            <label>{{ __('messages.action.bulk_update') }}</label>
                            <button type="button" class="btn btn-primary btn-block" data-toggle="modal" data-target="#modal-bulk-update">
                                <i class="fa fa-fw fa-edit"></i> {{ __('messages.action.bulk_update') }}
                            </button>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <hr />
        <div class="row">
            <!-- dialog -->
            <div class="modal fade" id="modal-bulk-delete">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                        </div>
                        <div class="modal-body">
                            <p>{{ __('messages.do_you_want_delete') }} ?</p>
                        </div>
                        <div class="modal-footer">
                            <form action="{{ route('account.bulk_destroy') }}" method="post">
                                {{ csrf_field() }}
                                <input type="hidden" value="" id="bulk_destroy_value" name="ids">
                                <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="modal-bulk-locked">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                        </div>
                        <div class="modal-body">
                            <p>{{ __('messages.do_you_want_locked') }} ?</p>
                        </div>
                        <div class="modal-footer">
                            <form action="{{ route('account.bulk_locked') }}" method="post">
                                {{ csrf_field() }}
                                <input type="hidden" value="" id="bulk_locked_value" name="ids">
                                <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                <button type="submit" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.locked') }}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="modal-bulk-extend-expired">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                        </div>
                        <div class="modal-body">
                            <p>{{ __('messages.do_you_want_extend_expired') }} ?</p>
                        </div>
                        <div class="modal-footer">
                            <form action="{{ route('account.bulk_extend_expired') }}" method="post">
                                {{ csrf_field() }}
                                <input type="hidden" value="" id="bulk_extend_expired_value" name="ids">
                                <label for="nome">{{ __('messages.time_expired') }}</label>
                                <br />
                                <input type="date" name="time_expired" class="form-control pull-right" style="width: 50%" maxlength="30" minlength="4" placeholder="{{ __('messages.time_expired') }}" required="" value="now()->format('Y-m-d')" autofocus>
                                <br /><br />
                                <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                <button type="submit" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.extend') }}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="modal-bulk-update">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title"><i class="fa fa-edit"></i> {{ __('messages.action.bulk_update') }}</h4>
                        </div>
                        <div class="modal-body">
                            <form action="{{ route('account.bulk_update') }}" method="post">
                                {{ csrf_field() }}
                                <input type="hidden" value="" id="bulk_update_value" name="ids">

                                <div class="form-group">
                                    <label for="time_expired">{{ __('messages.time_expired') }}</label>
                                    <input type="date" name="time_expired" class="form-control" placeholder="{{ __('messages.time_expired') }}" value="{{ now()->format('Y-m-d') }}">
                                    <small class="text-muted">{{ __('messages.leave_empty_to_keep_current_value') }}</small>
                                </div>

                                <div class="form-group">
                                    <label for="province">{{ __('messages.province') }}</label>
                                    <select name="province" id="bulk_province" class="form-control select2" data-placeholder="{{ __('messages.province') }}">
                                        <option value="-1">{{ __('messages.all') }}</option>
                                        @foreach($provinces as $key => $province)
                                            <option value="{{ $key }}">{{ $province['name'] }}</option>
                                        @endforeach
                                    </select>
                                    <small class="text-muted">{{ __('messages.leave_empty_to_keep_current_value') }}</small>
                                </div>

                                <div class="form-group">
                                    <label for="district">{{ __('messages.district') }}</label>
                                    <select name="district" id="bulk_district" class="form-control select2" data-placeholder="{{ __('messages.district') }}">
                                        <option value="-1">{{ __('messages.all') }}</option>
                                        @foreach($selectedDistricts as $key => $selectedDistrict)
                                            <option value="{{ $key }}">{{ $selectedDistrict['name'] }}</option>
                                        @endforeach
                                    </select>
                                    <small class="text-muted">{{ __('messages.leave_empty_to_keep_current_value') }}</small>
                                </div>

                                <div class="form-group">
                                    <label for="status">{{ __('messages.status') }}</label>
                                    <select name="status" class="form-control">
                                        <option value="">{{ __('messages.leave_empty_to_keep_current_value') }}</option>
                                        <option value="{{ App\Models\Account::STATUS_ACTIVE }}">{{ __('messages.status.active') }}</option>
                                        <option value="{{ App\Models\Account::STATUS_LOCK }}">{{ __('messages.status.locked') }}</option>
                                    </select>
                                </div>

                                <div class="form-group text-right">
                                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                    <button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> {{ __('messages.action.update') }}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="box-body">
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive">
                    @if(!isset($viewMode) || $viewMode == 'account')
                    <!-- Account View -->
                    <table id="tabelapadrao" class="table table-hover">
                        <thead>
                        <tr>
                            <th style="width: 30px;"><input type="checkbox" id="checkbox_master"></th>
                            <th style="width: 120px;">@sortablelink('name', __('messages.name'))</th>
                            <th style="width: 150px;">@sortablelink('name_org', __('messages.name_org'))</th>
                            <th style="width: 100px;">{{ __('messages.phone_number') }}</th>
                            <th style="width: 150px;">{{ __('messages.email') }}</th>
                            <th style="width: 120px;">@sortablelink('province', __('messages.province'))</th>
                            <th style="width: 120px;">@sortablelink('district', __('messages.district'))</th>
                            <th class="text-center" style="width: 80px;">{{ __('messages.status') }}</th>
                            <th style="width: 100px;">{{ __('messages.time_expired') }}</th>
                            <th class="text-center" style="width: 80px;">{{ __('messages.class') }}</th>
                            <th class="text-center" style="width: 100px;">{{ __('messages.account_type') }}</th>
                            <th class="text-center" style="width: 80px;">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($accounts as $account)
                        <tr>
                            <td><input type="checkbox" class="checkbox_row" data-id="{{$account->id}}"></td>
                            <td>
                                {{ $account->name }}
                            </td>
                            <td>{{ $account->name_org }}</td>
                            <td>{{ $account->phone_number }}</td>
                            <td>{{ $account->email }}</td>
                            <td>{{ $account->province != null && array_key_exists($account->province, $provinces) ? $provinces[$account->province . '']['name'] : '' }}</td>
                            <td>{{ $account->district != null && array_key_exists($account->district, $districts) ? $districts[$account->district . '']['name'] : '' }}</td>
                            <td class="text-center">
                                @if($account->status == 1)
                                <span class="account-status-indicator" title="{{ __('messages.status.active') }}" style="padding: 8px 10px; border-radius: 4px; display: inline-flex; align-items: center; justify-content: center; background-color: #5cb85c; color: white;"><i class="fa fa-unlock" style="font-size: 18px; line-height: 1;"></i></span>
                                @else
                                <span class="account-status-indicator" title="{{ __('messages.status.locked') }}" style="padding: 8px 10px; border-radius: 4px; display: inline-flex; align-items: center; justify-content: center; background-color: #d9534f; color: white;"><i class="fa fa-lock" style="font-size: 18px; line-height: 1;"></i></span>
                                @endif
                            </td>
                            <td class="text-center">{{ $account->time_expired != null ? \Carbon\Carbon::parse($account->time_expired)->format('d-m-Y') : null }}</td>
                            <td class="text-center">{{ $account->class }}</td>
                            <td class="text-center">
                                @if(isset($account->mamnon_account_type_id) && $account->mamnonAccountType)
                                    {{ $account->mamnonAccountType->name }}<br>
                                @endif
                                @if(isset($account->tieuhoc_account_type_id) && $account->tieuhocAccountType)
                                    {{ $account->tieuhocAccountType->name }}
                                @endif
                            </td>
                            <td class="text-center">
                                <a class="btn btn-default btn-xs" href="{{ route('account.show', $account->id) }}" title="See {{ $account->name }}"><i class="fa fa-eye"></i></a>
                                <a class="btn btn-danger btn-xs" href="#" title="Delete {{ $account->name}}" data-toggle="modal" data-target="#modal-delete-{{ $account->id }}"><i class="fa fa-trash"></i></a>
                            </td>
                        </tr>
                        <div class="modal fade" id="modal-delete-{{ $account->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>{{ __('messages.do_you_want_delete') }} ({{ $account->name }}) ?</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                        <a href="{{ route('account.destroy', $account->id) }}"><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                        </tbody>
                    </table>
                    <div class="col-md-12 text-center">
                        {{ $accounts->appends(request()->input())->links() }}
                    </div>
                    @else
                    <!-- School View -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="box box-primary">
                                <div class="box-header with-border">
                                    <h3 class="box-title" style="font-weight: bold; color: #333;">{{ __('messages.dashboard') }}</h3>
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                                    </div>
                                </div>
                                <div class="box-body" style="padding: 10px;">
                                    @php
                                        $partnerCount = $schools->filter(function($school) {
                                            return $school->school_type === 'partner';
                                        })->count();

                                        $customerCount = $schools->filter(function($school) {
                                            return $school->school_type === 'customer';
                                        })->count();

                                        $internalCount = $schools->filter(function($school) {
                                            return $school->school_type === 'internal';
                                        })->count();
                                    @endphp

                                    <!-- Nav tabs -->
                                    <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 0; border-bottom: 2px solid #3c8dbc;">
                                        <li role="presentation" class="active">
                                            <a href="#statistics-tab" aria-controls="statistics-tab" role="tab" data-toggle="tab" style="padding: 8px 15px; font-weight: bold;">
                                                <i class="fa fa-bar-chart"></i> {{ __('messages.statistics') }}
                                            </a>
                                        </li>
                                        <li role="presentation">
                                            <a href="#location-tab" aria-controls="location-tab" role="tab" data-toggle="tab" style="padding: 8px 15px; font-weight: bold;">
                                                <i class="fa fa-map"></i> {{ __('messages.province') }}/{{ __('messages.district') }}
                                            </a>
                                        </li>
                                    </ul>

                                    <!-- Tab panes -->
                                    <div class="tab-content" style="padding: 15px; background-color: #f9f9f9; border: 1px solid #ddd; border-top: none;">
                                        <!-- Statistics Tab -->
                                        <div role="tabpanel" class="tab-pane active" id="statistics-tab">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="info-box" style="min-height: 70px; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                                                <span class="info-box-icon bg-blue" style="height: 70px; width: 70px; font-size: 28px;"><i class="fa fa-building"></i></span>
                                                                <div class="info-box-content" style="margin-left: 70px; padding: 10px;">
                                                                    <span class="info-box-text" style="font-size: 14px; font-weight: bold;">{{ __('messages.schools') }}</span>
                                                                    <span class="info-box-number" style="font-size: 20px;">{{ $schools->count() }}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="info-box" style="min-height: 70px; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                                                <span class="info-box-icon bg-green" style="height: 70px; width: 70px; font-size: 28px;"><i class="fa fa-graduation-cap"></i></span>
                                                                <div class="info-box-content" style="margin-left: 70px; padding: 10px;">
                                                                    <span class="info-box-text" style="font-size: 14px; font-weight: bold;">{{ __('messages.class') }}</span>
                                                                    <span class="info-box-number" style="font-size: 20px;">89</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="info-box" style="min-height: 70px; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                                                <span class="info-box-icon bg-gray" style="height: 70px; width: 70px; font-size: 28px;"><i class="fa fa-tablet"></i></span>
                                                                <div class="info-box-content" style="margin-left: 70px; padding: 10px;">
                                                                    <span class="info-box-text" style="font-size: 14px; font-weight: bold;">{{ __('messages.device') }}</span>
                                                                    <span class="info-box-number" style="font-size: 20px;">2</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div style="background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                                        <h4 style="margin-top: 0; margin-bottom: 15px; font-weight: bold; color: #333;">{{ __('messages.school_types') }}</h4>
                                                        @php
                                                            $totalSchools = $schools->count();
                                                            $partnerPercentage = $totalSchools > 0 ? round(($partnerCount / $totalSchools) * 100, 1) : 0;
                                                            $customerPercentage = $totalSchools > 0 ? round(($customerCount / $totalSchools) * 100, 1) : 0;
                                                            $internalPercentage = $totalSchools > 0 ? round(($internalCount / $totalSchools) * 100, 1) : 0;
                                                        @endphp
                                                        <!-- No chart container needed -->
                                                        <div style="display: flex; justify-content: space-around; margin-top: 10px;">
                                                            <div style="text-align: center;">
                                                                <span class="label label-primary" style="font-size: 12px; padding: 5px 8px; display: block; margin-bottom: 5px;">{{ __('messages.partner') }}</span>
                                                                <span style="font-weight: bold; display: block;">{{ $partnerCount }} ({{ $partnerPercentage }}%)</span>
                                                            </div>
                                                            <div style="text-align: center;">
                                                                <span class="label label-success" style="font-size: 12px; padding: 5px 8px; display: block; margin-bottom: 5px;">{{ __('messages.customer') }}</span>
                                                                <span style="font-weight: bold; display: block;">{{ $customerCount }} ({{ $customerPercentage }}%)</span>
                                                            </div>
                                                            <div style="text-align: center;">
                                                                <span class="label label-default" style="font-size: 12px; padding: 5px 8px; display: block; margin-bottom: 5px;">{{ __('messages.internal') }}</span>
                                                                <span style="font-weight: bold; display: block;">{{ $internalCount }} ({{ $internalPercentage }}%)</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Location Tab (Combined Province and District) -->
                                        <div role="tabpanel" class="tab-pane" id="location-tab">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div style="background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                                                        <h4 style="margin-top: 0; margin-bottom: 10px; font-weight: bold; color: #333;">{{ __('messages.province') }} & {{ __('messages.district') }}</h4>
                                                        <div class="table-responsive" style="max-height: 550px; overflow-y: auto;">
                                                            <table class="table table-bordered table-hover" style="margin-bottom: 0;" id="provinceDistrictTable">
                                                                <thead>
                                                                    <tr style="background-color: #f4f4f4;">
                                                                        <th style="padding: 8px; font-weight: bold;">{{ __('messages.province') }}/{{ __('messages.district') }}</th>
                                                                        <th style="width: 300px; padding: 8px; font-weight: bold;">{{ __('messages.total') }}</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    @php
                                                                        // Group schools by province
                                                                        $provinceGroups = $schools->groupBy('province');
                                                                        // Sort by province name
                                                                        $sortedProvinces = $provinceGroups->sortKeys();

                                                                        // Group schools by district
                                                                        $districtGroups = $schools->groupBy('district');
                                                                        // Group districts by province
                                                                        $districtsByProvince = [];
                                                                        foreach ($districtGroups as $districtId => $districtSchools) {
                                                                            if ($districtId != null) {
                                                                                $firstSchool = $districtSchools->first();
                                                                                if ($firstSchool && $firstSchool->province != null) {
                                                                                    if (!isset($districtsByProvince[$firstSchool->province])) {
                                                                                        $districtsByProvince[$firstSchool->province] = [];
                                                                                    }
                                                                                    $districtsByProvince[$firstSchool->province][$districtId] = $districtSchools;
                                                                                }
                                                                            }
                                                                        }
                                                                    @endphp

                                                                    @foreach($sortedProvinces as $provinceId => $provinceSchools)
                                                                        @if($provinceId != null && array_key_exists($provinceId, $provinces))
                                                                            <tr class="province-row" data-province="{{ $provinceId }}">
                                                                                <td style="padding: 8px;">
                                                                                    <i class="fa fa-caret-right toggle-icon" id="icon-{{ $provinceId }}"></i>
                                                                                    <strong>{{ $provinces[$provinceId . '']['name'] }}</strong>
                                                                                </td>
                                                                                <td style="padding: 8px;">
                                                                                    @php
                                                                                        $provinceCount = $provinceSchools->count();
                                                                                        $provincePercentage = $schools->count() > 0 ? round(($provinceCount / $schools->count()) * 100, 1) : 0;
                                                                                    @endphp
                                                                                    <div class="progress-container" style="display: flex; align-items: center;">
                                                                                        <div class="progress" style="flex-grow: 1; margin-right: 10px; height: 15px;">
                                                                                            <div class="progress-bar progress-bar-primary" role="progressbar"
                                                                                                aria-valuenow="{{ $provincePercentage }}" aria-valuemin="0" aria-valuemax="100"
                                                                                                style="width: {{ $provincePercentage }}%;">
                                                                                            </div>
                                                                                        </div>
                                                                                        <span class="badge bg-blue" style="font-size: 12px; padding: 4px 8px; min-width: 80px; text-align: center;">
                                                                                            {{ $provinceCount }} ({{ $provincePercentage }}%)
                                                                                        </span>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>

                                                                            @if(isset($districtsByProvince[$provinceId]))
                                                                                @foreach($districtsByProvince[$provinceId] as $districtId => $districtSchools)
                                                                                    @if($districtId != null && array_key_exists($districtId, $districts))
                                                                                        <tr class="district-row" data-province="{{ $provinceId }}" style="display: none;">
                                                                                            <td style="padding: 8px; padding-left: 25px;">
                                                                                                <i class="fa fa-angle-double-right" style="margin-right: 5px;"></i>
                                                                                                {{ $districts[$districtId . '']['name'] }}
                                                                                            </td>
                                                                                            <td style="padding: 8px;">
                                                                                                @php
                                                                                                    $districtCount = $districtSchools->count();
                                                                                                    $districtPercentage = $provinceSchools->count() > 0 ? round(($districtCount / $provinceSchools->count()) * 100, 1) : 0;
                                                                                                @endphp
                                                                                                <div class="progress-container" style="display: flex; align-items: center;">
                                                                                                    <div class="progress" style="flex-grow: 1; margin-right: 10px; height: 12px;">
                                                                                                        <div class="progress-bar progress-bar-success" role="progressbar"
                                                                                                            aria-valuenow="{{ $districtPercentage }}" aria-valuemin="0" aria-valuemax="100"
                                                                                                            style="width: {{ $districtPercentage }}%;">
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <span class="badge bg-green" style="font-size: 11px; padding: 3px 6px; min-width: 70px; text-align: center;">
                                                                                                        {{ $districtCount }} ({{ $districtPercentage }}%)
                                                                                                    </span>
                                                                                                </div>
                                                                                            </td>
                                                                                        </tr>
                                                                                    @endif
                                                                                @endforeach
                                                                            @endif
                                                                        @endif
                                                                    @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <table id="tabelapadrao" class="table table-hover">
                        <thead>
                        <tr>
                            <th style="width: 50px;">{{ __('messages.stt') }}</th>
                            <th style="width: 150px;">@sortablelink('name', __('messages.name_org'))</th>
                            <th style="width: 120px;">@sortablelink('province', __('messages.province'))</th>
                            <th style="width: 120px;">@sortablelink('district', __('messages.district'))</th>
                            <th style="width: 100px;">{{ __('messages.time_expired') }}</th>
                            <th style="width: 120px;">{{ __('messages.software_type') }}</th>
                            <th style="width: 120px;">{{ __('messages.account_type') }}</th>
                            <th style="width: 120px;">@sortablelink('school_type', __('messages.school_type') ?: 'School Type')</th>
                            <th class="text-center" style="width: 80px;">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @forelse($schools as $index => $school)
                        <tr>
                            <td>{{ $schools->firstItem() + $index }}</td>
                            <td>{{ $school->name }}</td>
                            <td>{{ $school->province != null && array_key_exists($school->province, $provinces) ? $provinces[$school->province . '']['name'] : '' }}</td>
                            <td>{{ $school->district != null && array_key_exists($school->district, $districts) ? $districts[$school->district . '']['name'] : '' }}</td>
                            <td class="text-center">{{ isset($school->time_expired) && $school->time_expired != null ? \Carbon\Carbon::parse($school->time_expired)->format('d-m-Y') : null }}</td>
                            <td>
                                @if(isset($school->is_mamnon) && $school->is_mamnon == 1)
                                    {{ __('messages.software_mamnon') }}<br>
                                @endif
                                @if(isset($school->is_tieuhoc) && $school->is_tieuhoc == 1)
                                    {{ __('messages.software_tieuhoc') }}
                                @endif
                            </td>
                            <td>
                                @if(isset($school->is_mamnon) && $school->is_mamnon == 1 && $school->mamnonAccountType)
                                    {{ $school->mamnonAccountType->name }}<br>
                                @endif
                                @if(isset($school->is_tieuhoc) && $school->is_tieuhoc == 1 && $school->tieuhocAccountType)
                                    {{ $school->tieuhocAccountType->name }}
                                @endif
                            </td>
                            <td>
                                @php
                                    $schoolTypeValue = $school->school_type;
                                    $schoolType = '';
                                    $labelClass = '';

                                    if ($schoolTypeValue === 'partner') {
                                        $schoolType = __('messages.partner') ?: 'Partner';
                                        $labelClass = 'label-primary';
                                    } elseif ($schoolTypeValue === 'customer') {
                                        $schoolType = __('messages.customer') ?: 'Customer';
                                        $labelClass = 'label-success';
                                    } else {
                                        $schoolType = __('messages.internal') ?: 'Internal';
                                        $labelClass = 'label-default';
                                    }
                                @endphp
                                <span class="label {{ $labelClass }}">{{ $schoolType }}</span>
                            </td>
                            <td class="text-center">
                                <a class="btn btn-default btn-xs" href="{{ route('school.show', $school->id) }}" title="View {{ $school->name }}"><i class="fa fa-eye"></i></a>
                                <a class="btn btn-danger btn-xs" href="#" title="Delete {{ $school->name}}" data-toggle="modal" data-target="#modal-delete-school-{{ $school->id }}"><i class="fa fa-trash"></i></a>
                            </td>
                        </tr>
                        <div class="modal fade" id="modal-delete-school-{{ $school->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>{{ __('messages.do_you_want_delete') }} ({{ $school->name }}) ?</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                        <a href="{{ route('school.destroy', $school->id) }}"><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <tr>
                            <td colspan="9" class="text-center">{{ __('messages.no_schools_found') ?: 'No schools found' }}</td>
                        </tr>
                        @endforelse
                        </tbody>
                    </table>
                    <div class="col-md-12 text-center">
                        {{ $schools->appends(request()->input())->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $('#province').on('change', function(e){
        e.preventDefault();
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        let province = $('#province').val();
        let districtDOM = $('#district');
        $.ajax({
            type:'GET',
            url: "/api/v1/provinces/{id}/districts".replace("{id}", province),
            success:function(response){
                $('option', districtDOM).remove();
                $('#district').append('<option value="-1">{{ __('messages.all') }}</option>');
                $.each(response.data, function(){
                    $('<option/>', {
                        'value': this.code,
                        'text': this.name
                    }).appendTo('#district');
                });
            }

        });
    });

    // Bulk update province change handler
    $('#bulk_province').on('change', function(e){
        e.preventDefault();
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        let province = $('#bulk_province').val();
        let districtDOM = $('#bulk_district');
        $.ajax({
            type:'GET',
            url: "/api/v1/provinces/{id}/districts".replace("{id}", province),
            success:function(response){
                $('option', districtDOM).remove();
                $('#bulk_district').append('<option value="-1">{{ __('messages.all') }}</option>');
                $.each(response.data, function(){
                    $('<option/>', {
                        'value': this.code,
                        'text': this.name
                    }).appendTo('#bulk_district');
                });
            }
        });
    });

    // Toggle button functionality
    $('.btn-toggle button').on('click', function() {
        const mode = $(this).data('mode');
        $('#view_mode').val(mode);

        // Update button styles
        $('.btn-toggle button').removeClass('btn-primary active').addClass('btn-default');
        $(this).removeClass('btn-default').addClass('btn-primary active');

        // Submit the form to switch views
        $('#search-form').submit();
    });

    $(document).ready(function () {
        // Show the location tab by default to ensure charts are visible
        $('a[href="#location-tab"]').tab('show');

        // Remember active tab
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            localStorage.setItem('lastActiveSchoolTab', $(e.target).attr('href'));

            // Initialize collapsible rows when switching to location tab
            if ($(e.target).attr('href') === '#location-tab') {
                setTimeout(function() {
                    initCollapsibleRows();
                }, 200);
            }
        });

        // Check if there is a previously saved tab
        var lastActiveTab = localStorage.getItem('lastActiveSchoolTab');
        if (lastActiveTab) {
            $('a[href="' + lastActiveTab + '"]').tab('show');
        }

        // Remember dashboard collapse state
        $('.box-primary .btn-box-tool').on('click', function() {
            setTimeout(function() {
                var isCollapsed = $('.box-primary').hasClass('collapsed-box');
                localStorage.setItem('schoolDashboardCollapsed', isCollapsed ? '1' : '0');
            }, 100);
        });

        // Check if dashboard was previously collapsed
        var wasCollapsed = localStorage.getItem('schoolDashboardCollapsed');
        if (wasCollapsed === '1') {
            $('.box-primary').addClass('collapsed-box');
            $('.box-primary .box-body').hide();
            $('.box-primary .btn-box-tool i').removeClass('fa-minus').addClass('fa-plus');
        }

        // Initialize collapsible rows if in school view mode
        @if(isset($viewMode) && $viewMode == 'school')

            // Initialize collapsible rows
            function initCollapsibleRows() {
                // Make sure all district rows are hidden by default
                $('.district-row').hide();

                // Make sure all icons are in the correct state
                $('.toggle-icon').removeClass('fa-caret-down').addClass('fa-caret-right');

                // Add click handler for all province rows
                $('.province-row').each(function() {
                    var provinceId = $(this).data('province');

                    // Remove any existing onclick attribute
                    $(this).removeAttr('onclick');

                    // Add a direct click handler
                    $(this).off('click').on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleDistricts(provinceId, e);
                        return false;
                    });
                });

                console.log("Collapsible rows initialized");
            }

            // Call the initialization function
            initCollapsibleRows();

            // No chart initialization needed

            // Initialize collapsible rows when switching to the location tab
            $('a[href="#location-tab"]').on('shown.bs.tab', function (e) {
                initCollapsibleRows();
            });
        @endif

        // Checkbox master functionality
        $('#checkbox_master').on('click', function(e) {
            if($(this).is(':checked',true))
            {
                $(".checkbox_row").prop('checked', true);
            } else {
                $(".checkbox_row").prop('checked',false);
            }

            let allVals = [];
            $(".checkbox_row:checked").each(function() {
                allVals.push($(this).attr('data-id'));
            });

            $('#bulk_destroy_value').val("");
            $('#bulk_destroy_value').val(allVals.join(','));

            $('#bulk_locked_value').val("");
            $('#bulk_locked_value').val(allVals.join(','));

            $('#bulk_extend_expired_value').val("");
            $('#bulk_extend_expired_value').val(allVals.join(','));

            $('#bulk_update_value').val("");
            $('#bulk_update_value').val(allVals.join(','));
        });

        // Individual checkbox functionality
        $('.checkbox_row').on('click', function(e) {
            let allVals = [];
            $(".checkbox_row:checked").each(function() {
                allVals.push($(this).attr('data-id'));
            });

            $('#bulk_destroy_value').val("");
            $('#bulk_destroy_value').val(allVals.join(','));

            $('#bulk_locked_value').val("");
            $('#bulk_locked_value').val(allVals.join(','));

            $('#bulk_extend_expired_value').val("");
            $('#bulk_extend_expired_value').val(allVals.join(','));

            $('#bulk_update_value').val("");
            $('#bulk_update_value').val(allVals.join(','));
        });

        // Status toggle functionality has been removed - icons are now indicators only
    });
</script>

<!-- Add direct script to ensure charts are visible and handle collapsible functionality -->
<script>
    // Simple function to toggle districts visibility
    function toggleDistricts(provinceId, event) {
        // Get all district rows for this province
        var districtRows = document.querySelectorAll('.district-row[data-province="' + provinceId + '"]');
        var icon = document.getElementById('icon-' + provinceId);

        // Check if districts are visible
        var isVisible = false;
        if (districtRows.length > 0) {
            isVisible = districtRows[0].style.display === 'table-row';
        }

        // Toggle visibility
        for (var i = 0; i < districtRows.length; i++) {
            districtRows[i].style.display = isVisible ? 'none' : 'table-row';
        }

        // Toggle icon
        if (icon) {
            if (isVisible) {
                icon.classList.remove('fa-caret-down');
                icon.classList.add('fa-caret-right');
            } else {
                icon.classList.remove('fa-caret-right');
                icon.classList.add('fa-caret-down');
            }
        }

        // Prevent event propagation
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        return false; // Prevent default action
    }

    // This script will run after the page is fully loaded
    window.addEventListener('load', function() {
        // Hide all district rows by default
        var districtRows = document.querySelectorAll('.district-row');
        for (var i = 0; i < districtRows.length; i++) {
            districtRows[i].style.display = 'none';
        }

        // Reset all toggle icons
        var toggleIcons = document.querySelectorAll('.toggle-icon');
        for (var i = 0; i < toggleIcons.length; i++) {
            toggleIcons[i].classList.remove('fa-caret-down');
            toggleIcons[i].classList.add('fa-caret-right');
        }

        // Apply direct click handlers to all province rows
        var provinceRows = document.querySelectorAll('.province-row');
        for (var i = 0; i < provinceRows.length; i++) {
            var row = provinceRows[i];
            var provinceId = row.getAttribute('data-province');

            // Remove any existing onclick attribute
            row.removeAttribute('onclick');

            // Add a direct click event listener
            (function(id) {
                row.addEventListener('click', function(e) {
                    toggleDistricts(id, e);
                });
            })(provinceId);
        }

        // Force show the location tab after a short delay
        setTimeout(function() {
            if (document.querySelector('a[href="#location-tab"]')) {
                // Trigger a click on the location tab
                document.querySelector('a[href="#location-tab"]').click();

                // Reapply click handlers after the tab is shown
                setTimeout(function() {
                    // Apply direct click handlers to all province rows again
                    var provinceRows = document.querySelectorAll('.province-row');
                    for (var i = 0; i < provinceRows.length; i++) {
                        var row = provinceRows[i];
                        var provinceId = row.getAttribute('data-province');

                        // Remove any existing onclick attribute
                        row.removeAttribute('onclick');

                        // Add a direct click event listener
                        (function(id, r) {
                            r.addEventListener('click', function(e) {
                                toggleDistricts(id, e);
                            });
                        })(provinceId, row);
                    }
                }, 200);
            }
        }, 500);
    });

    // Function to initialize the location tab
    function initLocationTab() {
        console.log("Initializing location tab");

        // Force show the location tab
        if (document.querySelector('a[href="#location-tab"]')) {
            document.querySelector('a[href="#location-tab"]').click();
        }
    }
</script>
@endsection