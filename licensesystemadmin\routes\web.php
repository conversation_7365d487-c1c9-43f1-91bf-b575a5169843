<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Route::impersonate();
Auth::routes();

Route::get('/', 'App\Http\Controllers\HomeController@index');
Route::get('/home', 'App\Http\Controllers\HomeController@index')->name('home');
Route::get('/config', 'App\Http\Controllers\ConfigController@index')->name('config');
Route::put('/config/update/{id}', 'App\Http\Controllers\ConfigController@update')->name('config.update');
//Route::post('/config/store/permission_group', 'App\Http\Controllers\ConfigController@storePermissionGroup')->name('config.store.permission_group');
//Route::put('/config/update/permission_group/{id}', 'App\Http\Controllers\ConfigController@updatePermissionGroup')->name('config.update.permission_group');
//Route::post('/config/store/permission', 'App\Http\Controllers\ConfigController@storePermission')->name('config.store.permission');
//Route::put('/config/update/permission/{id}', 'App\Http\Controllers\ConfigController@updatePermission')->name('config.update.permission');

Route::group(['namespace' => 'App\Http\Controllers\Profile'], function (){
	Route::get('/profile', 'ProfileController@index')->name('profile');
	Route::put('/profile/update/profile/{id}', 'ProfileController@updateProfile')->name('profile.update.profile');
	Route::put('/profile/update/password/{id}', 'ProfileController@updatePassword')->name('profile.update.password');
	Route::put('/profile/update/avatar/{id}', 'ProfileController@updateAvatar')->name('profile.update.avatar');
});

Route::group(['namespace' => 'App\Http\Controllers\Error'], function (){
	Route::get('/unauthorized', 'ErrorController@unauthorized')->name('unauthorized');
});

Route::group(['namespace' => 'App\Http\Controllers\User'], function (){
	// Users
	Route::get('/user', 'UserController@index')->name('user');
	Route::get('/user/create', 'UserController@create')->name('user.create');
	Route::post('/user/store', 'UserController@store')->name('user.store');
	Route::get('/user/edit/{id}', 'UserController@edit')->name('user.edit');
	Route::put('/user/update/{id}', 'UserController@update')->name('user.update');
	Route::get('/user/edit/password/{id}', 'UserController@editPassword')->name('user.edit.password');
	Route::put('/user/update/password/{id}', 'UserController@updatePassword')->name('user.update.password');
	Route::get('/user/show/{id}', 'UserController@show')->name('user.show');
	Route::get('/user/destroy/{id}', 'UserController@destroy')->name('user.destroy');

	// Roles
	Route::get('/role', 'RoleController@index')->name('role');
	Route::get('/role/create', 'RoleController@create')->name('role.create');
	Route::post('/role/store', 'RoleController@store')->name('role.store');
	Route::get('/role/edit/{id}', 'RoleController@edit')->name('role.edit');
	Route::put('/role/update/{id}', 'RoleController@update')->name('role.update');
	Route::get('/role/show/{id}', 'RoleController@show')->name('role.show');
	Route::get('/role/destroy/{id}', 'RoleController@destroy')->name('role.destroy');
});

// Accounts
Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'account'], function (){
    Route::get('/', 'AccountController@index')->name('account');
    Route::get('/create', 'AccountController@create')->name('account.create');
    Route::post('/store', 'AccountController@store')->name('account.store');
    Route::get('/export', 'AccountController@export')->name('account.export');
    Route::post('/import', 'AccountController@import')->name('account.import');
    Route::get('/export-template', 'AccountController@exportTemplate')->name('account.export_template');
    Route::get('/edit/{id}', 'AccountController@edit')->name('account.edit');
    Route::put('/update/{id}', 'AccountController@update')->name('account.update');
    Route::get('/show/{id}', 'AccountController@show')->name('account.show');
    Route::get('/destroy/{id}', 'AccountController@destroy')->name('account.destroy');
    Route::post('/bulk-destroy', 'AccountController@bulkDestroy')->name('account.bulk_destroy');
    Route::post('/bulk-locked', 'AccountController@bulkLocked')->name('account.bulk_locked');
    Route::post('/bulk-extend-expired', 'AccountController@bulkExtendExpired')->name('account.bulk_extend_expired');
    Route::post('/bulk-update', 'AccountController@bulkUpdate')->name('account.bulk_update');
    Route::post('/toggle-status/{id}', 'AccountController@toggleStatus')->name('account.toggle_status');
    // Commented out removed functionality
    // Route::get('/refresh-token/{id}', 'AccountController@refreshToken')->name('account.refresh_token');
    Route::get('/extend-time-expired/{id}', 'AccountController@extendTimeExpired')->name('account.extend_time_expired');
    Route::post('/update-time-expired/{id}', 'AccountController@updateTimeExpired')->name('account.update_time_expired');
    // Route::get('/reset-computer/{id}', 'AccountController@resetComputer')->name('account.reset_computer');
    // Route::get('/reset-password/{id}', 'AccountController@resetPassword')->name('account.reset_password');
    Route::post('/toggle-status/{id}', 'AccountController@toggleStatus')->name('account.toggle_status');
});

// Commented out pending account feature
// Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'account-pending'], function (){
//     Route::get('/', 'AccountPendingController@index')->name('account_pending');
//     Route::get('/accept/{id}', 'AccountPendingController@accept')->name('account_pending.accept');
//     Route::get('/destroy/{id}', 'AccountPendingController@destroy')->name('account_pending.delete');
// });

Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'course'], function (){
    Route::get('/', 'Course\CourseController@index')->name('course');
    Route::get('/create', 'Course\CourseController@create')->name('course.create');
    Route::post('/store', 'Course\CourseController@store')->name('course.store');
    Route::get('/edit/{id}', 'Course\CourseController@edit')->name('course.edit');
    Route::put('/update/{id}', 'Course\CourseController@update')->name('course.update');
    Route::get('/destroy/{id}', 'Course\CourseController@destroy')->name('course.destroy');
    Route::post('/bulk-destroy', 'Course\CourseController@bulkDestroy')->name('course.bulk_destroy');
});

Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'lesson'], function (){
    Route::get('/', 'Course\LessonController@index')->name('lesson');
    Route::get('/create', 'Course\LessonController@create')->name('lesson.create');
    Route::post('/store', 'Course\LessonController@store')->name('lesson.store');
    Route::get('/edit/{id}', 'Course\LessonController@edit')->name('lesson.edit');
    Route::put('/update/{id}', 'Course\LessonController@update')->name('lesson.update');
    Route::get('/destroy/{id}', 'Course\LessonController@destroy')->name('lesson.destroy');
});

// Redirect material index to course index with material view mode
Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'material'], function (){
    Route::get('/', function() {
        return redirect()->route('course', ['view_mode' => 'material']);
    })->name('material');
    Route::get('/create', 'Course\MaterialController@create')->name('material.create');
    Route::post('/store', 'Course\MaterialController@store')->name('material.store');
    Route::get('/edit/{id}', 'Course\MaterialController@edit')->name('material.edit');
    Route::put('/update/{id}', 'Course\MaterialController@update')->name('material.update');
    Route::get('/destroy/{id}', 'Course\MaterialController@destroy')->name('material.destroy');
    Route::post('/bulk-destroy', 'Course\MaterialController@bulkDestroy')->name('material.bulk_destroy');
});

Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'account-type'], function (){
    Route::get('/', 'AccountTypeController@index')->name('account_type');
    Route::get('/create', 'AccountTypeController@create')->name('account_type.create');
    Route::post('/store', 'AccountTypeController@store')->name('account_type.store');
    Route::get('/edit/{id}', 'AccountTypeController@edit')->name('account_type.edit');
    Route::put('/update/{id}', 'AccountTypeController@update')->name('account_type.update');
    Route::get('/destroy/{id}', 'AccountTypeController@destroy')->name('account_type.destroy');
    Route::get('/materials', 'AccountTypeController@getMaterials')->name('account-type.materials');
    Route::post('/update-materials/{id}', 'AccountTypeController@updateMaterials')->name('account_type.update_materials');
});

Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'account-backup'], function (){
    Route::get('/', 'AccountBackupController@index')->name('account_backup');
    Route::get('/download/{id}', 'AccountBackupController@download')->name('account_backup.download');
});

Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'address'], function (){
    Route::get('/', 'AddressController@index')->name('address');
    Route::get('/download', 'AddressController@download')->name('address.download');
});

// Schools
Route::group(['namespace' => 'App\Http\Controllers', 'prefix' => 'school'], function (){
    // Redirect school index to account index with school view mode
    Route::get('/', function() {
        return redirect()->route('account', ['view_mode' => 'school']);
    })->name('school');

    Route::get('/create', 'SchoolController@create')->name('school.create');
    Route::post('/store', 'SchoolController@store')->name('school.store');
    Route::get('/edit/{id}', 'SchoolController@edit')->name('school.edit');
    Route::put('/update/{id}', 'SchoolController@update')->name('school.update');
    Route::get('/show/{id}', 'SchoolController@show')->name('school.show');
    Route::get('/destroy/{id}', 'SchoolController@destroy')->name('school.destroy');
    Route::get('/{id}/accounts-status', 'SchoolController@getAccountsStatus')->name('school.accounts_status');
    Route::post('/{id}/toggle-accounts-status', 'SchoolController@toggleAccountsStatus')->name('school.toggle_accounts_status');
});
