@extends('layouts.AdminLTE.index')

@section('icon_page', 'user')

@section('title', __('messages.index.account.game'))

@section('css')
<style>
    /* Ensure table columns have appropriate widths */
    #tabelapadrao th, #tabelapadrao td {
        padding: 12px 8px; /* Increased padding for taller rows */
        vertical-align: middle;
        border: 1px solid #ddd;
    }

    /* Improve table appearance */
    #tabelapadrao {
        border-collapse: collapse;
        border-spacing: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #ddd;
    }

    /* Alternate row colors for better readability */
    #tabelapadrao tbody tr:nth-child(odd) {
        background-color: #f9f9f9;
    }

    #tabelapadrao tbody tr:hover {
        background-color: #e8f4fc;
    }

    /* Ensure buttons are properly sized and spaced */
    .btn-xs {
        padding: 5px 10px;
        font-size: 12px;
        line-height: 1.5;
        border-radius: 3px;
        margin: 0 3px;
    }
</style>
@endsection

@section('menu_pagina')

@endsection

@section('content')

<div class="box box-primary">
    <div class="box-body">
        <div class="row">
            <form action="{{ route('account_pending') }}" method="get">
                <div class="col-lg-3">
                    <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                        <label for="nome">{{ __('messages.search') }}</label>
                        <input type="text" name="search" class="form-control" placeholder="{{ __('messages.search') }}" value="{{ $keyword }}">
                    </div>
                </div>
                <div class="col-lg-1">
                    <label for="nome">{{ __('messages.filter') }} / {{ __('messages.search') }}</label>
                    <button type="submit" class="btn btn-google pull-right"><i class="fa fa-fw fa-search"></i> {{ __('messages.search') }}</button>
                </div>
            </form>
        </div>
    </div>
    <div class="box-body">
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive">
                    <table id="tabelapadrao" class="table table-condensed table-bordered table-hover">
                        <thead>
                        <tr>
                            <th width="5%"></th>
                            <th width="12%">@sortablelink('name', __('messages.name'))</th>
                            <th width="15%">@sortablelink('name_org', __('messages.name_org'))</th>
                            <th width="10%">{{ __('messages.phone_number') }}</th>
                            <th width="15%">{{ __('messages.email') }}</th>
                            <th width="10%">@sortablelink('province', __('messages.province'))</th>
                            <th width="10%">@sortablelink('district', __('messages.district'))</th>
                            <th width="8%" class="text-center">{{ __('messages.status') }}</th>
                            <th width="10%">{{ __('messages.time_expired') }}</th>
                            <th width="5%" class="text-center">{{ __('messages.class') }}</th>
                            <th width="10%" class="text-center">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php $stt = 1; @endphp
                        @foreach($accounts as $account)
                        <tr>
                            <td class="text-center">{{ $stt++ }}</td>
                            <td>
                                {{ $account->name }}
                            </td>
                            <td>{{ $account->name_org }}</td>
                            <td>{{ $account->phone_number }}</td>
                            <td>{{ $account->email }}</td>
                            <td>{{ $account->province != null && array_key_exists($account->province, $provinces) ? $provinces[$account->province . '']['name'] : '' }}</td>
                            <td>{{ $account->district != null && array_key_exists($account->district, $districts) ? $districts[$account->district . '']['name'] : '' }}</td>
                            <td class="text-center">
                                <span class="label label-warning" style="padding: 8px 10px; border-radius: 4px; display: inline-flex; align-items: center; justify-content: center;" title="{{ __('messages.status.pending') }}"><i class="fa fa-clock-o" style="font-size: 18px; line-height: 1;"></i></span>
                            </td>
                            <td class="text-center">{{ $account->time_expired != null ? \Carbon\Carbon::parse($account->time_expired)->format('d-m-Y') : null }}</td>
                            <td class="text-center">{{ $account->class }}</td>
                            <td class="text-center">
                                <a class="btn btn-danger  btn-xs" href="#" title="Delete {{ $account->name}}" data-toggle="modal" data-target="#modal-delete-{{ $account->id }}"><i class="fa fa-trash"></i></a>
                                <a class="btn btn-primary  btn-xs" href="#" title="Approve {{ $account->name}}" data-toggle="modal" data-target="#modal-approve-{{ $account->id }}"><i class="fa fa-arrow-circle-right"></i></a>
                            </td>
                        </tr>
                        <div class="modal fade" id="modal-delete-{{ $account->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>{{ __('messages.do_you_want_delete') }} ({{ $account->name }}) ?</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cancel</button>
                                        <a href="{{ route('account_pending.delete', $account->id) }}"><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade" id="modal-approve-{{ $account->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>{{ __('messages.do_you_want_approve') }} ({{ $account->name }}) ?</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cancel</button>
                                        <a href="{{ route('account_pending.accept', $account->id) }}"><button type="button" class="btn btn-primary"><i class="fa fa-arrow-circle-right"></i> {{ __('messages.action.approve') }}</button></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-12 text-center">
                {{ $accounts->appends(request()->input())->links() }}
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {

    });
</script>

@endsection
