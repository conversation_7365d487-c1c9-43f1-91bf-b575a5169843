@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.add.user'))

@section('menu_pagina')

	<li role="presentation">
		<a href="{{ route('user') }}" class="link_menu_page">
			<i class="fa fa-user"></i> {{ __('messages.index.user') }}
		</a>
	</li>

@endsection

@section('content')

    <div class="box box-primary">
		<div class="box-body">
			<div class="row">
				<div class="col-md-12">
					 <form action="{{ route('user.store') }}" method="post">
                        {{ csrf_field() }}
                        <input type="hidden" name="active" value="1">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.name') }}</label>
                                    <input type="text" name="name" class="form-control" maxlength="30" minlength="4" placeholder="{{ __('messages.name') }}" required="" value="{{ old('name') }}" autofocus>
                                    @if($errors->has('name'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('name') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('email') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.email') }}</label>
                                    <input type="email" name="email" class="form-control" placeholder="{{ __('messages.email') }}" required="" value="{{ old('email') }}">
                                    @if($errors->has('email'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('email') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('password') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.password') }}</label>
                                    <input type="password" name="password" class="form-control" placeholder="{{ __('messages.password') }}" minlength="6" required="">
                                    @if($errors->has('password'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('password') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('password-confirm') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.confirm_password') }}</label>
                                    <input type="password" name="password_confirmation" class="form-control" placeholder="{{ __('messages.confirm_password') }}" minlength="6" required="">
                                    @if($errors->has('password-confirm'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('password-confirm') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="form-group {{ $errors->has('roles') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.user.role') }}</label>
                                    <select name="roles[]" class="form-control select2" multiple="multiple" data-placeholder="{{ __('messages.user.role') }}" required="">
                                        @foreach($roles as $role)
                                            @if($role->id != 1)
                                                <option value="{{ $role->id}}"> {{ $role->name}} </option>
                                            @endif
                                        @endforeach
                                    </select>
                                    @if($errors->has('roles'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('roles') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6"></div>
                            <div class="col-lg-6">
                               <button type="submit" class="btn btn-primary pull-right"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.add') }}</button>
                            </div>
                        </div>
                    </form>
				</div>
			</div>
		</div>
	</div>

@endsection

@section('layout_js')

    <script>
        $(function(){
            $('.select2').select2({
                "language": {
                    "noResults": function(){
                        return "Nenhum registro encontrado.";
                    }
                }
            });
        });

    </script>

@endsection
