@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', __('messages.add.school'))

@section('menu_pagina')
    <li role="presentation">
        <a href="{{ route('school') }}" class="link_menu_page">
            <i class="fa fa-school"></i> {{ __('messages.index.school') }}
        </a>
    </li>
@endsection

@section('content')
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">{{ __('messages.add.school') }}</h3>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('school.store') }}" method="post">
                        {{ csrf_field() }}
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('name') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.name') }}</label>
                                    <input type="text" name="name" class="form-control" placeholder="{{ __('messages.name') }}" required="" value="{{ old('name') }}">
                                    @if($errors->has('name'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('name') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('software_types') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.software_type') }}</label>
                                    <select name="software_types[]" class="form-control select2" id="software-type" multiple="multiple" data-placeholder="{{ __('messages.software_type') }}" required="">
                                        <option value="{{ \App\Models\Account::SOFTWARE_MAMNON }}" selected> {{ __('messages.software_mamnon') }} </option>
                                        <option value="{{ \App\Models\Account::SOFTWARE_TIEUHOC }}" selected> {{ __('messages.software_tieuhoc') }} </option>
                                    </select>
                                    @if($errors->has('software_types'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('software_types') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('province') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.province') }}</label>
                                    <select name="province" class="form-control select2" id="province" data-placeholder="{{ __('messages.province') }}">
                                        <option value="">{{ __('messages.select') }}</option>
                                        @foreach($provinces as $key => $province)
                                            <option value="{{ $key }}" @if(old('province') == $key) selected @endif>{{ $province['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('province'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('province') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('district') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.district') }}</label>
                                    <select name="district" class="form-control select2" id="district" data-placeholder="{{ __('messages.district') }}">
                                        <option value="">{{ __('messages.select') }}</option>
                                        @foreach($firstDistricts as $key => $district)
                                            <option value="{{ $key }}" @if(old('district') == $key) selected @endif>{{ $district['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('district'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('district') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group {{ $errors->has('time_expired') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.time_expired') }}</label>
                                    <input type="date" name="time_expired" class="form-control" placeholder="{{ __('messages.time_expired') }}" value="{{ old('time_expired') }}">
                                    @if($errors->has('time_expired'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('time_expired') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6" id="mamnon-account-type">
                                <div class="form-group {{ $errors->has('mamnon_account_type') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.mamnon_account_type') }}</label>
                                    <select name="mamnon_account_type" class="form-control select2" data-placeholder="{{ __('messages.mamnon_account_type') }}">
                                        <option value="">{{ __('messages.select') }}</option>
                                        @foreach($mamnonAccountTypes as $accountType)
                                            <option value="{{ $accountType->id }}" @if(old('mamnon_account_type') == $accountType->id) selected @endif>{{ $accountType->name }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('mamnon_account_type'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('mamnon_account_type') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6" id="tieuhoc-account-type">
                                <div class="form-group {{ $errors->has('tieuhoc_account_type') ? 'has-error' : '' }}">
                                    <label for="nome">{{ __('messages.tieuhoc_account_type') }}</label>
                                    <select name="tieuhoc_account_type" class="form-control select2" data-placeholder="{{ __('messages.tieuhoc_account_type') }}">
                                        <option value="">{{ __('messages.select') }}</option>
                                        @foreach($tieuhocAccountTypes as $accountType)
                                            <option value="{{ $accountType->id }}" @if(old('tieuhoc_account_type') == $accountType->id) selected @endif>{{ $accountType->name }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('tieuhoc_account_type'))
                                        <span class="help-block">
                                            <strong>{{ $errors->first('tieuhoc_account_type') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <button type="submit" class="btn btn-primary pull-left"><i class="fa fa-fw fa-plus"></i> {{ __('messages.action.add') }}</button>
                                <a href="{{ route('school') }}" class="btn btn-danger pull-left" style="margin-left: 10px;"><i class="fa fa-fw fa-times"></i> {{ __('messages.action.cancel') }}</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('layout_js')
    <script>
        $(function(){
            $('.select2').select2({
                "language": {
                    "noResults": function(){
                        return "Nenhum registro encontrado.";
                    }
                }
            });
        });

        $('#software-type').on('change', function(e){
            let sofwareTypes = $('#software-type').val();
            if(sofwareTypes.includes('{{ \App\Models\Account::SOFTWARE_MAMNON }}')){
                $('#mamnon-account-type').show();
            } else {
                $('#mamnon-account-type').hide();
            }

            if(sofwareTypes.includes('{{ \App\Models\Account::SOFTWARE_TIEUHOC }}')){
                $('#tieuhoc-account-type').show();
            } else {
                $('#tieuhoc-account-type').hide();
            }
        });

        $('#province').on('change', function(e){
            var provinceId = $(this).val();
            if(!provinceId){
                $('#district').empty();
                $('#district').append('<option value="">{{ __('messages.select') }}</option>');
                return;
            }

            $.ajax({
                url: "{{ route('api.districts') }}",
                type: "GET",
                data: {
                    province_id: provinceId
                },
                success: function(data){
                    $('#district').empty();
                    $('#district').append('<option value="">{{ __('messages.select') }}</option>');
                    $.each(data, function(key, value){
                        $('#district').append('<option value="' + key + '">' + value.name + '</option>');
                    });
                }
            });
        });
    </script>
@endsection
