@extends('layouts.AdminLTE.index')

@section('icon_page', 'school')

@section('title', __('messages.index.school'))

@section('menu_pagina')
    <li role="presentation">
        <a href="{{ route('school.create') }}" class="link_menu_page">
            <i class="fa fa-plus"></i> {{ __('messages.add.school') }}
        </a>
    </li>
@endsection

@section('content')
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">{{ __('messages.filter') }}</h3>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="box-body">
            <div class="row">
                <form action="{{ route('school') }}" method="get">
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="nome">{{ __('messages.search') }}</label>
                            <input type="text" name="search" class="form-control" placeholder="{{ __('messages.search') }}" value="{{ $keyword }}">
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="form-group">
                            <label for="nome">{{ __('messages.province') }}</label>
                            <select name="province" class="form-control select2" id="province" data-placeholder="{{ __('messages.province') }}">
                                <option value="-1">{{ __('messages.all') }}</option>
                                @foreach($provinces as $key => $province)
                                    <option value="{{ $key }}" @if($searchProvince == $key) selected @endif>{{ $province['name'] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="form-group">
                            <label for="nome">{{ __('messages.district') }}</label>
                            <select name="district" class="form-control select2" id="district" data-placeholder="{{ __('messages.district') }}">
                                <option value="-1">{{ __('messages.all') }}</option>
                                @foreach($selectedDistricts as $key => $district)
                                    <option value="{{ $key }}" @if($searchDistrict == $key) selected @endif>{{ $district['name'] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <div class="form-group">
                            <label for="nome">&nbsp;</label>
                            <button type="submit" class="btn btn-primary form-control"><i class="fa fa-search"></i> {{ __('messages.action.search') }}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <table id="tabelapadrao" class="table table-condensed table-bordered table-hover">
                        <thead>
                        <tr>
                            <th style="width: 5%">#</th>
                            <th>@sortablelink('name', __('messages.name'))</th>
                            <th>@sortablelink('province', __('messages.province'))</th>
                            <th>@sortablelink('district', __('messages.district'))</th>
                            <th>{{ __('messages.software_type') }}</th>
                            <th>@sortablelink('time_expired', __('messages.time_expired'))</th>
                            <th class="text-center" style="width: 10%">{{ __('messages.action') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($schools as $index => $school)
                            <tr>
                                <td>{{ ($schools->currentPage() - 1) * $schools->perPage() + $index + 1 }}</td>
                                <td>{{ $school->name }}</td>
                                <td>{{ $school->province != null && array_key_exists($school->province, $provinces) ? $provinces[$school->province . '']['name'] : '' }}</td>
                                <td>{{ $school->district != null && array_key_exists($school->district, $districts) ? $districts[$school->district . '']['name'] : '' }}</td>
                                <td>
                                    @if($school->is_mamnon == 1)
                                        {{ __('messages.software_mamnon') }}
                                        @if($school->is_tieuhoc == 1)
                                            , {{ __('messages.software_tieuhoc') }}
                                        @endif
                                    @elseif($school->is_tieuhoc == 1)
                                        {{ __('messages.software_tieuhoc') }}
                                    @endif
                                </td>
                                <td>{{ $school->time_expired ? date('d-m-Y', strtotime($school->time_expired)) : '' }}</td>
                                <td class="text-center">
                                    <a class="btn btn-default btn-xs" href="{{ route('school.show', $school->id) }}" title="View/Edit {{ $school->name }}"><i class="fa fa-eye"></i></a>
                                    <a class="btn btn-danger btn-xs" href="#" title="Delete {{ $school->name}}" data-toggle="modal" data-target="#modal-delete-{{ $school->id }}"><i class="fa fa-trash"></i></a>
                                </td>
                            </tr>
                            <div class="modal fade" id="modal-delete-{{ $school->id }}">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">×</span>
                                            </button>
                                            <h4 class="modal-title"><i class="fa fa-warning"></i> {{ __('messages.alert') }}!!</h4>
                                        </div>
                                        <div class="modal-body">
                                            <p>{{ __('messages.do_you_want_delete') }} ({{ $school->name }}) ?</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default pull-left" data-dismiss="modal">{{ __('messages.action.cancel') }}</button>
                                            <a href="{{ route('school.destroy', $school->id) }}"><button type="button" class="btn btn-danger"><i class="fa fa-trash"></i> {{ __('messages.action.delete') }}</button></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        </tbody>
                    </table>
                    <div class="col-md-12 text-center">
                        {{ $schools->appends(request()->input())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('layout_js')
    <script>
        $(function(){
            $('.select2').select2({
                "language": {
                    "noResults": function(){
                        return "Nenhum registro encontrado.";
                    }
                }
            });
        });

        $('#province').on('change', function(e){
            var provinceId = $(this).val();
            if(provinceId == -1){
                $('#district').empty();
                $('#district').append('<option value="-1">{{ __('messages.all') }}</option>');
                return;
            }

            $.ajax({
                url: "{{ route('api.districts') }}",
                type: "GET",
                data: {
                    province_id: provinceId
                },
                success: function(data){
                    $('#district').empty();
                    $('#district').append('<option value="-1">{{ __('messages.all') }}</option>');
                    $.each(data, function(key, value){
                        $('#district').append('<option value="' + key + '">' + value.name + '</option>');
                    });
                }
            });
        });
    </script>
@endsection
